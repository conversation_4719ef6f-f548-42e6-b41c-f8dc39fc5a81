import Foundation
import SwiftData
import SwiftUI

/// 水印设置管理器
class WatermarkSettingsManager {
    // 单例
    static let shared = WatermarkSettingsManager()
    
    // 模型容器和上下文
    private var modelContainer: ModelContainer?
    private var modelContext: ModelContext?
    
    // 私有初始化方法
    private init() {
        setupModelContainer()
    }
    
    // 设置SwiftData模型容器
    private func setupModelContainer() {
        // 使用共享容器替代单独创建容器
        self.modelContainer = SharedContainer.shared.container
        if let container = self.modelContainer {
            self.modelContext = ModelContext(container)
            print(" WatermarkSettingsManager: 已使用共享 ModelContainer 和 ModelContext。")
        } else {
            print(" WatermarkSettingsManager: 获取共享 ModelContainer 失败！")
        }
    }
    
    // MARK: - 公共方法
    
    /// 获取水印设置
    func getSettings() -> WatermarkSettings {
        print(" WatermarkSettingsManager: getSettings() - 开始获取设置")
        guard let context = modelContext else {
            print(" WatermarkSettingsManager: getSettings() - ModelContext 不可用。返回临时的默认设置。")
            return WatermarkSettings() // 返回临时的内存中对象，不保存
        }
        
        do {
            let descriptor = FetchDescriptor<WatermarkSettings>(predicate: #Predicate { $0.id == "watermark_settings" })
            print(" WatermarkSettingsManager: getSettings() - 尝试从 SwiftData 获取设置...")
            let existingSettings = try context.fetch(descriptor)
            
            if let settings = existingSettings.first {
                print(" WatermarkSettingsManager: getSettings() - 成功获取到已存在的设置。activeWatermarkStyleType: \(settings.activeWatermarkStyleType)")
                return settings
            } else {
                print(" WatermarkSettingsManager: getSettings() - 未找到已存在的设置。将创建、插入并保存新的默认设置。")
                let newSettings = WatermarkSettings() // activeWatermarkStyleType 默认为 "none"
                context.insert(newSettings)
                do {
                    try context.save()
                    print(" WatermarkSettingsManager: getSettings() - 新的默认设置已成功保存到 SwiftData。")
                } catch {
                    print(" WatermarkSettingsManager: getSettings() - 保存新的默认设置到 SwiftData 失败: \(error.localizedDescription)")
                    // 即使保存失败，也返回这个新创建的内存中对象，避免应用卡死
                }
                return newSettings
            }
        } catch {
            print(" WatermarkSettingsManager: getSettings() - 从 SwiftData 获取设置失败: \(error.localizedDescription)。返回临时的默认设置。")
            return WatermarkSettings() // 读取失败，返回临时的内存中对象，不保存，以避免覆盖有效数据
        }
    }
    
    /// 保存设置
    func saveSettings(_ settings: WatermarkSettings) {
        print(" WatermarkSettingsManager: saveSettings() - 准备保存设置。activeWatermarkStyleType: \(settings.activeWatermarkStyleType)")
        guard let context = modelContext else {
            print(" WatermarkSettingsManager: saveSettings() - 保存失败：ModelContext 不可用")
            return
        }
        
        do {
            settings.updateTimestamp()
            // SwiftData 会自动跟踪已插入对象的更改，只需调用 save()
            // 如果 settings 对象是从 context fetch 出来的，直接修改它就会被跟踪。
            // 如果 settings 是一个新创建的或者从 getSettings() 返回的（可能和 context 中的不是同一个实例，虽然内容一样），
            // 确保它是被 context 管理的。但 getSettings() 的逻辑已经处理了插入新对象。
            // updateSetting 方法中， settings = getSettings() 保证了我们操作的是 context 中的对象（或新插入的）。
            try context.save()
            print(" WatermarkSettingsManager: saveSettings() - 设置已成功保存到 SwiftData。")
        } catch {
            print(" WatermarkSettingsManager: saveSettings() - 保存水印设置到 SwiftData 失败: \(error.localizedDescription)")
        }
    }
    
    /// 更新特定设置
    func updateSetting<T>(_ keyPath: WritableKeyPath<WatermarkSettings, T>, value: T) {
        var settings = getSettings() // 获取当前（可能已持久化的）设置对象
        let oldValueDescription = "\(settings[keyPath: keyPath])"
        settings[keyPath: keyPath] = value
        print(" WatermarkSettingsManager: updateSetting() - 更新键路径 \(String(describing: keyPath)) 从 '\(oldValueDescription)' 到 '\(value)'.")
        saveSettings(settings) // 保存更改
        
        // 如果更新的是水印类型，发送通知
        if keyPath == \WatermarkSettings.activeWatermarkStyleType {
            NotificationCenter.default.post(
                name: Notification.Name("WatermarkTypeChanged"),
                object: value
            )
            print(" WatermarkSettingsManager: 发送水印类型变更通知，新类型: \(value)")
        }
    }
    
    /// 重置所有设置为默认值
    func resetToDefaults() {
        guard let context = modelContext else {
            return
        }
        
        do {
            // 删除所有现有设置
            let descriptor = FetchDescriptor<WatermarkSettings>()
            let existingSettings = try context.fetch(descriptor)
            for settings in existingSettings {
                context.delete(settings)
            }
            
            // 创建新的默认设置
            let newSettings = WatermarkSettings()
            context.insert(newSettings)
            try context.save()
        } catch {
            print("重置水印设置失败: \(error.localizedDescription)")
        }
    }
} 