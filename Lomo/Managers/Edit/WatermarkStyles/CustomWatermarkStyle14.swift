import UIKit

// MARK: - 自定义水印14
/// 自定义水印样式14 - 基于自定义水印12复制的水印
class CustomWatermarkStyle14: WatermarkStyle {
    /// 上边框宽度
    private let topBorderWidth: CGFloat
    
    /// 底部边框宽度
    private let bottomBorderWidth: CGFloat
    
    /// 左右边框宽度
    private let sideBorderWidth: CGFloat
    
    /// 边框颜色
    private let borderColor: UIColor
    
    // --- 存储原始状态和中间视图 ---
    private weak var originalContentView: UIView?
    private var originalFrameInSuperview: CGRect?
    private var originalTransform: CGAffineTransform?
    private weak var originalSuperview: UIView?
    private var originalIndexInSuperview: Int?
    private weak var scaledCustom14FrameWrapper: UIView? // 用于存储最外层的、已缩放的自定义14框架视图
    
    // --- 水印元素相关 ---
    private weak var bottomItemsContainer: UIView? // 底部边框中的元素容器
    private weak var logoView: UIImageView? // Logo图片视图
    private weak var textLabel: UILabel? // 水印文字标签
    private weak var descriptionLabel: UILabel? // 水印描述标签
    private weak var preferenceLabel: UILabel? // 偏好选项标签
    private weak var signatureLabel: UILabel? // 署名标签
    private var watermarkSettings: WatermarkSettings? // 保存水印设置的引用
    private var separatorLabel: UILabel? // "|"分隔符标签

    // 添加新属性存储分隔符视图
    private weak var separatorView: SeparatorLineView?

    /// 自定义分隔线视图类
    private class SeparatorLineView: UIView {
        private let lineColor: UIColor
        private var _lineWidth: CGFloat = 1.5
        
        var lineWidth: CGFloat {
            return _lineWidth
        }
        
        init(color: UIColor, width: CGFloat = 1.5) {
            self.lineColor = color
            self._lineWidth = width
            super.init(frame: .zero)
            self.backgroundColor = .clear
        }
        
        func updateLineWidth(_ width: CGFloat) {
            self._lineWidth = width
            self.setNeedsDisplay()
        }
        
        required init?(coder: NSCoder) {
            fatalError("init(coder:) has not been implemented")
        }
        
        override func draw(_ rect: CGRect) {
            guard let context = UIGraphicsGetCurrentContext() else { return }
            
            // 设置线条宽度和颜色
            context.setLineWidth(min(rect.width, _lineWidth))
            context.setStrokeColor(lineColor.cgColor)
            
            // 绘制垂直线条 - 居中
            let centerX = rect.width / 2.0
            context.move(to: CGPoint(x: centerX, y: 0))
            context.addLine(to: CGPoint(x: centerX, y: rect.height))
            
            // 应用绘制
            context.strokePath()
        }
    }

    /// 初始化
    /// - Parameters:
    ///   - topBorderWidth: 上边框宽度
    ///   - bottomBorderWidth: 底部边框宽度
    ///   - sideBorderWidth: 左右边框宽度
    ///   - borderColor: 边框颜色，默认为白色
    init(topBorderWidth: CGFloat, 
         bottomBorderWidth: CGFloat, // From WatermarkConstants.Custom13.bottomBorderScreenHeightFactor * screenHeight
         sideBorderWidth: CGFloat, 
         borderColor: UIColor = WatermarkConstants.Colors.borderWhite) {
        self.topBorderWidth = topBorderWidth
        self.bottomBorderWidth = bottomBorderWidth
        self.sideBorderWidth = sideBorderWidth
        self.borderColor = borderColor
    }
    
    /// 查找实际的内容视图 (与BorderWatermarkStyle中的方法类似)
    private func findActualContentView(in container: UIView) -> UIView? {
        if let imageView = container.subviews.first(where: { $0 is UIImageView }) {
            return imageView
        }
        if let intermediateContainer = container.viewWithTag(100) {
            if let cameraPreview = intermediateContainer.viewWithTag(101) {
                return cameraPreview
            }
        }
        if container is UIImageView || container.tag == 101 {
             return container
        }
        print("⚠️ CustomWatermarkStyle14: 未找到实际的内容视图。Container: \(container)")
        return container.subviews.first
    }

    /// 应用自定义水印14到预览视图
    func apply(to previewContainer: UIView) {
        // 安全检查：确保容器有效
        guard previewContainer.bounds.width > 0, previewContainer.bounds.height > 0 else {
            print("⚠️ CustomWatermarkStyle14: apply - 无效的容器尺寸，跳过应用水印")
            return
        }
        
        if scaledCustom14FrameWrapper != nil {
            remove(from: previewContainer)
        }

        guard let contentView = findActualContentView(in: previewContainer) else {
            print("❌ CustomWatermarkStyle14: apply - 无法找到实际内容视图。")
            return
        }
        
        // 确保内容视图尺寸有效
        guard contentView.bounds.width > 0, contentView.bounds.height > 0 else {
            print("⚠️ CustomWatermarkStyle14: apply - 内容视图尺寸无效，跳过应用水印")
            return
        }

        // 获取水印设置
        self.watermarkSettings = WatermarkSettingsManager.shared.getSettings()
        // 获取模糊边框设置
        let isBlurBorderEnabled = watermarkSettings?.isBlurBorderEnabled ?? false
        let blurIntensity = watermarkSettings?.blurIntensity ?? 0.7
        // 获取阴影效果设置
        let isShadowEnabled = watermarkSettings?.isShadowEnabled ?? false

        // 1. 保存原始状态
        self.originalContentView = contentView
        self.originalTransform = contentView.transform
        self.originalSuperview = contentView.superview
        if let superview = contentView.superview {
            self.originalFrameInSuperview = contentView.frame
            self.originalIndexInSuperview = superview.subviews.firstIndex(of: contentView)
        } else {
            self.originalFrameInSuperview = contentView.bounds
            self.originalIndexInSuperview = 0
        }

        // --- 创建包含 contentView 和不对称边框的视图 (`custom14Wrapper`) ---
        let contentOriginalSize = contentView.bounds.size
        
        let wrapperWidth = contentOriginalSize.width + (2 * self.sideBorderWidth)
        let wrapperHeight = contentOriginalSize.height + self.topBorderWidth + self.bottomBorderWidth
        let wrapperSize = CGSize(width: wrapperWidth, height: wrapperHeight)

        let custom14Wrapper = UIView(frame: CGRect(origin: .zero, size: wrapperSize))
        custom14Wrapper.clipsToBounds = false // 允许子视图的阴影溢出
        
        // 根据模糊边框设置决定背景 - 两种模式完全互斥
        if isBlurBorderEnabled {
            // 模糊边框模式 - 背景透明
            custom14Wrapper.backgroundColor = .clear
            
            // 使用通用方法捕获内容视图并应用模糊效果
            if let capturedImage = WatermarkStyleUtils.captureView(contentView) {
                WatermarkStyleUtils.applyBlurBackground(to: custom14Wrapper, with: capturedImage, intensity: blurIntensity, settings: watermarkSettings)
            } else {
                // 截图失败时回退到默认背景
                print("⚠️ CustomWatermarkStyle14: 无法获取视图截图，使用默认背景")
                custom14Wrapper.backgroundColor = self.borderColor
            }
        } else {
            // 普通边框模式 - 设置背景颜色
            custom14Wrapper.backgroundColor = self.borderColor
        }
        
        contentView.transform = .identity
        contentView.frame = CGRect(x: self.sideBorderWidth, y: self.topBorderWidth, width: contentOriginalSize.width, height: contentOriginalSize.height)
        custom14Wrapper.addSubview(contentView)

        // 使用扩展方法应用阴影效果
        WatermarkStyleUtils.applyShadowEffect(to: contentView, in: custom14Wrapper, isShadowEnabled: isShadowEnabled, styleIdentifier: "CustomWatermarkStyle14")

        // --- 创建底部元素容器视图 ---
        let bottomItemsContainerFrame = CGRect(
            x: self.sideBorderWidth,
            y: self.topBorderWidth + contentOriginalSize.height,
            width: contentOriginalSize.width,
            height: self.bottomBorderWidth
        )
        
        let bottomContainer = UIView(frame: bottomItemsContainerFrame)
        bottomContainer.backgroundColor = .clear // 透明背景，因为wrapper已经提供了白色背景
        custom14Wrapper.addSubview(bottomContainer)
        self.bottomItemsContainer = bottomContainer
        
        // 添加水印元素到底部容器
        addWatermarkElementsToBottomContainer(bottomContainer)

        self.scaledCustom14FrameWrapper = custom14Wrapper
        previewContainer.addSubview(custom14Wrapper)

        // --- 将 wrapper (作为整体) 缩放并居中到 `previewContainer` ---
        let containerSize = previewContainer.bounds.size
        guard custom14Wrapper.bounds.width > 0, custom14Wrapper.bounds.height > 0 else {
            print("❌ CustomWatermarkStyle14: wrapper 尺寸为0。")
            custom14Wrapper.removeFromSuperview()
            restoreOriginalContentViewState() // 尝试恢复
            return
        }

        let scaleX = containerSize.width / custom14Wrapper.bounds.width
        let scaleY = containerSize.height / custom14Wrapper.bounds.height
        let finalScaleFactor = min(scaleX, scaleY)

        custom14Wrapper.transform = CGAffineTransform(scaleX: finalScaleFactor, y: finalScaleFactor)
        custom14Wrapper.center = CGPoint(x: containerSize.width / 2, y: containerSize.height / 2)
        
        print("✅ CustomWatermarkStyle14: 已应用自定义水印14。最终缩放: \(finalScaleFactor)")
    }

    /// 添加水印元素到底部容器
    private func addWatermarkElementsToBottomContainer(_ container: UIView) {
        guard let settings = self.watermarkSettings else { return }
        
        // 安全检查：确保容器尺寸有效
        guard container.bounds.width > 0, container.bounds.height > 0 else {
            print("⚠️ CustomWatermarkStyle14: 无效的容器尺寸，跳过添加水印元素")
            return
        }
        
        // 确定需要显示哪些元素
        let showLogo = !settings.selectedLogo.isEmpty // Logo不为空字符串表示启用
        let showText = settings.isWatermarkTextEnabled && !settings.watermarkText.isEmpty // 文字启用且不为空
        // 检查新格式或旧格式
        let showPreference = !settings.selectedPreferences.isEmpty || settings.preferenceOption != "OFF" // 存在选中的偏好选项或旧格式不为OFF
        // 检查署名是否启用
        let showSignature = settings.isWatermarkSignatureEnabled && !settings.watermarkSignature.isEmpty // 署名启用且不为空
        // 检查描述是否启用
        let showDescription = settings.isWatermarkDescriptionEnabled && !settings.watermarkDescription.isEmpty // 描述启用且不为空
        
        // 统计启用的元素数量
        let enabledElementsCount = [showLogo, showText, showPreference, showSignature].filter { $0 }.count
        
                // 如果没有任何元素启用，直接返回
        if enabledElementsCount == 0 { return }
        
        // 创建Logo（如果需要）
        if showLogo {
            createLogoView(container, settings: settings, enabledElementsCount: enabledElementsCount)
        }
        
        // 创建文字（如果需要）
        if showText {
            createTextLabel(container, settings: settings, enabledElementsCount: enabledElementsCount)
        }
        
        // 创建描述（如果需要）
        if showDescription {
            createDescriptionLabel(container, settings: settings, enabledElementsCount: enabledElementsCount)
        }
        
        // 创建偏好（如果需要）
        if showPreference {
            createPreferenceLabel(container, settings: settings, enabledElementsCount: enabledElementsCount)
        }
        
        // 创建署名（如果需要）
        if showSignature {
            createSignatureLabel(container, settings: settings, enabledElementsCount: enabledElementsCount)
        }
        
        // 如果需要分隔符
        if showLogo && (showText || showPreference) && showSignature {
            createSeparator(container, settings: settings)
        }
        
        // 直接安排布局，不使用异步处理
        arrangeElementsLayout(container, enabledCount: enabledElementsCount, 
                              showLogo: showLogo, 
                              showText: showText,
                              showDescription: showDescription,
                              showPreference: showPreference,
                              showSignature: showSignature)
    }
    
    /// 创建Logo视图
    private func createLogoView(_ container: UIView, settings: WatermarkSettings, enabledElementsCount: Int) {
        // 水印14使用动态大小 - 根据当前底部边框宽度等比例计算Logo大小
        // 公式：logoSize = bottomBorderWidth * logoSizeBaseBorderRatio
        let logoSize = self.bottomBorderWidth * WatermarkConstants.Custom14.logoSizeBaseBorderRatio
        
        let logoImageView = LogoCreator.createLogo(
            with: settings,
            fixedSize: logoSize
        )
        
        container.addSubview(logoImageView)
        self.logoView = logoImageView
        
        print("✅ 水印14 Logo动态大小: bottomBorderWidth=\(self.bottomBorderWidth), logoSize=\(logoSize)")
    }
    
    /// 创建文字标签
    private func createTextLabel(_ container: UIView, settings: WatermarkSettings, enabledElementsCount: Int) {
        // 水印14使用线性增长 - 文字只有25%增长，而Logo有50%增长
        let baseSize: CGFloat
        let growthFactor: CGFloat
        
        if enabledElementsCount == 1 {
            baseSize = WatermarkConstants.Custom14.fontSingleElementBaseSize
            growthFactor = WatermarkConstants.Custom14.fontSingleElementGrowthFactor
        } else {
            baseSize = WatermarkConstants.Custom14.fontTwoElementsBaseSize  
            growthFactor = WatermarkConstants.Custom14.fontTwoElementsGrowthFactor
        }
        
        // 线性增长公式：fontSize = baseSize + (currentBorderWidth - baseBorderWidth) * growthFactor
        let baseBorderWidth = UIScreen.main.bounds.height * WatermarkConstants.Custom14.baseBorderHeight
        let borderWidthDiff = self.bottomBorderWidth - baseBorderWidth
        let fontSize = UIScreen.main.bounds.height * (baseSize + borderWidthDiff / UIScreen.main.bounds.height * growthFactor)
        
        let label = TextLabelCreator.createLabel(
            with: settings, 
            enabledElementsCount: enabledElementsCount,
            fixedFontSize: fontSize // 使用动态计算的字体大小
        )
        
        // 设置尺寸
        let maxWidth = container.bounds.width * WatermarkConstants.Custom14.labelMaxWidthContainerWidthFactor
        let size = label.sizeThatFits(CGSize(width: maxWidth, height: CGFloat.greatestFiniteMagnitude))
        label.frame.size = size
        
        container.addSubview(label)
        self.textLabel = label
        
        print("✅ 水印14 文字线性增长: baseSize=\(baseSize*100)%, borderDiff=\((borderWidthDiff/UIScreen.main.bounds.height)*100)%, fontSize=\((fontSize/UIScreen.main.bounds.height)*100)%, enabledCount=\(enabledElementsCount)")
    }
    
    /// 创建描述标签
    private func createDescriptionLabel(_ container: UIView, settings: WatermarkSettings, enabledElementsCount: Int) {
        // 水印14使用线性增长 - 文字只有25%增长，而Logo有50%增长
        let baseSize: CGFloat
        let growthFactor: CGFloat
        
        if enabledElementsCount == 1 {
            baseSize = WatermarkConstants.Custom14.fontSingleElementBaseSize
            growthFactor = WatermarkConstants.Custom14.fontSingleElementGrowthFactor
        } else {
            baseSize = WatermarkConstants.Custom14.fontTwoElementsBaseSize  
            growthFactor = WatermarkConstants.Custom14.fontTwoElementsGrowthFactor
        }
        
        // 线性增长公式：fontSize = baseSize + (currentBorderWidth - baseBorderWidth) * growthFactor
        let baseBorderWidth = UIScreen.main.bounds.height * WatermarkConstants.Custom14.baseBorderHeight
        let borderWidthDiff = self.bottomBorderWidth - baseBorderWidth
        let fontSize = UIScreen.main.bounds.height * (baseSize + borderWidthDiff / UIScreen.main.bounds.height * growthFactor)
        
        let label = TextLabelCreator.createLabel(
            with: settings, 
            text: settings.watermarkDescription, // 使用watermarkDescription的文本
            enabledElementsCount: enabledElementsCount,
            fixedFontSize: fontSize // 使用动态计算的字体大小
        )
        
        // 设置尺寸
        let maxWidth = container.bounds.width * WatermarkConstants.Custom14.labelMaxWidthContainerWidthFactor
        let size = label.sizeThatFits(CGSize(width: maxWidth, height: CGFloat.greatestFiniteMagnitude))
        label.frame.size = size
        
        // 应用描述选项文字颜色逻辑
        WatermarkStyleUtils.applyDescriptionTextColor(to: label, with: settings, borderColor: self.borderColor, styleIdentifier: "水印14")
        
        container.addSubview(label)
        self.descriptionLabel = label
        
        print("✅ 水印14 描述线性增长: baseSize=\(baseSize*100)%, borderDiff=\((borderWidthDiff/UIScreen.main.bounds.height)*100)%, fontSize=\((fontSize/UIScreen.main.bounds.height)*100)%, enabledCount=\(enabledElementsCount)")
    }
    
    /// 创建偏好标签
    private func createPreferenceLabel(_ container: UIView, settings: WatermarkSettings, enabledElementsCount: Int) {
        // 水印14使用线性增长 - 偏好标签与文字使用相同的增长模式（25%增长）
        let baseSize: CGFloat
        let growthFactor: CGFloat
        
        if enabledElementsCount == 1 {
            baseSize = WatermarkConstants.Custom14.fontSingleElementBaseSize
            growthFactor = WatermarkConstants.Custom14.fontSingleElementGrowthFactor
        } else {
            baseSize = WatermarkConstants.Custom14.fontTwoElementsBaseSize
            growthFactor = WatermarkConstants.Custom14.fontTwoElementsGrowthFactor
        }
        
        // 线性增长公式：fontSize = baseSize + (currentBorderWidth - baseBorderWidth) * growthFactor
        let baseBorderWidth = UIScreen.main.bounds.height * WatermarkConstants.Custom14.baseBorderHeight
        let borderWidthDiff = self.bottomBorderWidth - baseBorderWidth
        let fontSize = UIScreen.main.bounds.height * (baseSize + borderWidthDiff / UIScreen.main.bounds.height * growthFactor)
        
        let label = PreferenceLabelCreator.createLabel(
            for: settings.preferenceOption, 
            with: settings, 
            enabledElementsCount: enabledElementsCount,
            fixedFontSize: fontSize // 使用动态计算的字体大小
        )
        
        // 设置尺寸
        let maxWidth = container.bounds.width * WatermarkConstants.Custom14.labelMaxWidthContainerWidthFactor
        let size = label.sizeThatFits(CGSize(width: maxWidth, height: CGFloat.greatestFiniteMagnitude))
        label.frame.size = size
        
        container.addSubview(label)
        self.preferenceLabel = label
        
        print("✅ 水印14 偏好线性增长: baseSize=\(baseSize*100)%, borderDiff=\((borderWidthDiff/UIScreen.main.bounds.height)*100)%, fontSize=\((fontSize/UIScreen.main.bounds.height)*100)%, enabledCount=\(enabledElementsCount)")
    }
    
    /// 创建署名标签
    private func createSignatureLabel(_ container: UIView, settings: WatermarkSettings, enabledElementsCount: Int) {
        // 水印14使用线性增长 - 署名大小与文字相同
        let baseSize: CGFloat
        let growthFactor: CGFloat
        
        if enabledElementsCount == 1 {
            baseSize = WatermarkConstants.Custom14.fontSingleElementBaseSize
            growthFactor = WatermarkConstants.Custom14.fontSingleElementGrowthFactor
        } else {
            baseSize = WatermarkConstants.Custom14.fontTwoElementsBaseSize  
            growthFactor = WatermarkConstants.Custom14.fontTwoElementsGrowthFactor
        }
        
        // 线性增长公式：fontSize = baseSize + (currentBorderWidth - baseBorderWidth) * growthFactor
        let baseBorderWidth = UIScreen.main.bounds.height * WatermarkConstants.Custom14.baseBorderHeight
        let borderWidthDiff = self.bottomBorderWidth - baseBorderWidth
        var fontSize = UIScreen.main.bounds.height * (baseSize + borderWidthDiff / UIScreen.main.bounds.height * growthFactor)
        
        // 应用署名大小乘数
        fontSize *= CGFloat(settings.signatureFontSizeMultiplier)
        
        // 使用TextUtils创建署名标签
        let label = TextUtils.createSignatureLabel(
            with: settings,
            fontSize: fontSize
        )
        
        // 设置尺寸
        let maxWidth = container.bounds.width * WatermarkConstants.Custom14.labelMaxWidthContainerWidthFactor
        let size = label.sizeThatFits(CGSize(width: maxWidth, height: CGFloat.greatestFiniteMagnitude))
        label.frame.size = size
        
        container.addSubview(label)
        self.signatureLabel = label
        
        print("✅ 水印14 署名线性增长: baseSize=\(baseSize*100)%, borderDiff=\((borderWidthDiff/UIScreen.main.bounds.height)*100)%, fontSize=\((fontSize/UIScreen.main.bounds.height)*100)%, enabledCount=\(enabledElementsCount), 大小乘数=\(settings.signatureFontSizeMultiplier)")
    }
    
    /// 创建分隔符标签
    private func createSeparator(_ container: UIView, settings: WatermarkSettings) {
        // 获取分隔符颜色 - 使用统一的工具方法
        let separatorColor = WatermarkStyleUtils.getSpecialElementColor(with: settings, borderColor: self.borderColor)
        
        // 默认分隔符线宽
        var separatorLineWidth: CGFloat = 1.5
        
        // 获取字体名称和粗细
        let fontName = settings.selectedFontName
        let fontWeight = settings.selectedFontWeight
        
        // 尝试根据右侧文本的字体获取对应的分隔符线宽
        if let textLabel = self.textLabel, let font = textLabel.font {
            // 使用新的FontLineThicknessUtils类来获取线宽
            separatorLineWidth = FontLineThicknessUtils.getLineThicknessForFont(font)
            print("✅ 分隔符: 使用字体[\(font.fontName)]的自定义映射获取线宽: \(separatorLineWidth)")
        } else if let preferenceLabel = self.preferenceLabel, let font = preferenceLabel.font {
            // 使用新的FontLineThicknessUtils类来获取线宽
            separatorLineWidth = FontLineThicknessUtils.getLineThicknessForFont(font)
            print("✅ 分隔符: 使用偏好字体[\(font.fontName)]的自定义映射获取线宽: \(separatorLineWidth)")
        } else if !fontName.isEmpty && !fontWeight.isEmpty {
            // 如果没有可用的标签，但有字体名称和粗细信息，使用这些信息获取线宽
            separatorLineWidth = FontLineThicknessUtils.getLineThickness(fontName: fontName, fontWeight: fontWeight)
            print("✅ 分隔符: 使用设置中的字体[\(fontName)-\(fontWeight)]获取线宽: \(separatorLineWidth)")
        } else {
            // 默认使用常规粗细
            separatorLineWidth = 1.5
            print("✅ 分隔符: 无法获取字体信息，使用默认线宽1.5")
        }
        
        // 创建自定义分隔符视图，传入颜色和线宽
        let separatorView = SeparatorLineView(color: separatorColor, width: separatorLineWidth)
        
        // 设置初始大小 - 宽度为线宽，高度暂时先设置较小
        separatorView.frame = CGRect(x: 0, y: 0, width: separatorLineWidth, height: 20)
        
        container.addSubview(separatorView)
        self.separatorLabel = nil // 清除旧的标签引用
        
        // 使用新属性保存分隔符视图引用
        self.separatorView = separatorView
        
        print("✅ 分隔符: 使用自定义线条视图，使用统一颜色逻辑，线宽为\(separatorLineWidth)")
    }
    
    /// 根据元素数量和类型安排布局
    private func arrangeElementsLayout(_ container: UIView, enabledCount: Int, 
                                      showLogo: Bool, showText: Bool, 
                                      showDescription: Bool,
                                      showPreference: Bool, showSignature: Bool) {
        let containerHeight = container.bounds.height
        let containerWidth = container.bounds.width
        
        // 使用基于屏幕宽度的内边距常量
        let leftPadding = UIScreen.main.bounds.width * WatermarkConstants.Custom14.logoLeftPaddingScreenWidthFactor
        let rightPadding = UIScreen.main.bounds.width * WatermarkConstants.Custom14.textRightPaddingScreenWidthFactor
        
        // 元素间距设为1%屏幕宽度
        let elementSpacing = UIScreen.main.bounds.width * 0.01
        
        // 添加Logo与署名间的特殊间距常量 - 2%屏幕宽度
        let logoSignatureSpacing = UIScreen.main.bounds.width * 0.02
        
        // 添加署名与描述间的特殊间距常量 - 2%屏幕宽度
        let signatureDescriptionSpacing = UIScreen.main.bounds.width * 0.02
        
        switch enabledCount {
        case 1:
            // 只有一个元素：居中显示 - 保持现有逻辑不变
            if let logoView = self.logoView {
                // Logo垂直居中
                logoView.center = CGPoint(x: containerWidth / 2, y: containerHeight / 2)
            } else if let textLabel = self.textLabel {
                // 文字垂直居中
                textLabel.center = CGPoint(x: containerWidth / 2, y: containerHeight / 2)
            } else if let preferenceLabel = self.preferenceLabel {
                // 偏好垂直居中
                preferenceLabel.center = CGPoint(x: containerWidth / 2, y: containerHeight / 2)
            } else if let signatureLabel = self.signatureLabel {
                // 署名垂直居中
                signatureLabel.center = CGPoint(x: containerWidth / 2, y: containerHeight / 2)
            } else if let descriptionLabel = self.descriptionLabel {
                // 描述垂直居中
                descriptionLabel.center = CGPoint(x: containerWidth / 2, y: containerHeight / 2)
            }
            
        case 2:
            if showLogo && showSignature {
                // Logo + 署名: Logo在左，署名在右，间距2%屏幕宽度，整体居中
                if let logoView = self.logoView, let signatureLabel = self.signatureLabel {
                    // 计算两个元素的总宽度（包括间距）
                    let totalWidth = logoView.bounds.width + logoSignatureSpacing + signatureLabel.bounds.width
                    
                    // 计算整体居中时的起始x坐标
                    let startX = (containerWidth - totalWidth) / 2
                    
                    // 放置Logo在左侧
                    logoView.center = CGPoint(
                        x: startX + logoView.bounds.width / 2,
                        y: containerHeight / 2
                    )
                    
                    // 放置署名在右侧
                    signatureLabel.center = CGPoint(
                        x: startX + logoView.bounds.width + logoSignatureSpacing + signatureLabel.bounds.width / 2,
                        y: containerHeight / 2
                    )
                    
                    print("✅ 水印14两元素布局: LOGO在左, 署名在右, 整体居中, 间距=\(logoSignatureSpacing)")
                }
                
            } else if showLogo && (showText || showPreference) {
                // Logo + 文字/偏好: Logo在左，文字/偏好在右，有分隔符，整体居中
                if let logoView = self.logoView {
                    // 创建分隔符视图（如果尚未创建）
                    if self.separatorView == nil && self.watermarkSettings != nil {
                        createSeparator(container, settings: self.watermarkSettings!)
                    }
                    
                    if let separator = self.separatorView {
                        // 分隔符高度设置
                        let textHeight: CGFloat
                        if let textLabel = self.textLabel {
                            textHeight = textLabel.bounds.height
                        } else if let preferenceLabel = self.preferenceLabel {
                            textHeight = preferenceLabel.bounds.height
                        } else {
                            textHeight = containerHeight * 0.8
                        }
                        
                        // 设置分隔符高度
                        separator.frame = CGRect(
                            x: separator.frame.origin.x,
                            y: separator.frame.origin.y,
                            width: separator.bounds.width,
                            height: textHeight
                        )
                        
                        // 获取分隔符线宽
                        let separatorLineWidth = separator.lineWidth
                        
                        // 设置动态间距，考虑分隔符线宽
                        let dynamicSpacing = elementSpacing + separatorLineWidth * 0.5
                        
                        // 计算所有元素的总宽度
                        let totalWidth: CGFloat
                        let rightElement: UIView?
                        
                        if let textLabel = self.textLabel, showText {
                            totalWidth = logoView.bounds.width + dynamicSpacing * 2 + separator.bounds.width + textLabel.bounds.width
                            rightElement = textLabel
                        } else if let preferenceLabel = self.preferenceLabel, showPreference {
                            totalWidth = logoView.bounds.width + dynamicSpacing * 2 + separator.bounds.width + preferenceLabel.bounds.width
                            rightElement = preferenceLabel
                        } else {
                            // 安全处理
                            logoView.center = CGPoint(x: containerWidth / 2, y: containerHeight / 2)
                            print("⚠️ 水印14: Logo存在但文字/偏好缺失")
                            return
                        }
                        
                        // 计算整体居中时的起始x坐标
                        let startX = (containerWidth - totalWidth) / 2
                        
                        // 放置Logo在左侧
                        logoView.center = CGPoint(
                            x: startX + logoView.bounds.width / 2,
                            y: containerHeight / 2
                        )
                        
                        // 放置分隔符
                        separator.center = CGPoint(
                            x: startX + logoView.bounds.width + dynamicSpacing,
                            y: containerHeight / 2
                        )
                        
                        // 放置右侧元素
                        if let rightElement = rightElement {
                            rightElement.center = CGPoint(
                                x: startX + logoView.bounds.width + dynamicSpacing * 2 + separator.bounds.width + rightElement.bounds.width / 2,
                                y: containerHeight / 2
                            )
                        }
                        
                        print("✅ 水印14两元素布局: LOGO在左, 文字/偏好在右, 有分隔符, 整体居中")
                    }
                }
                
            } else if showSignature && (showText || showPreference) {
                // 署名 + 文字/偏好: 署名在左，文字/偏好在右，有分隔符，整体居中
                if let signatureLabel = self.signatureLabel {
                    // 创建分隔符视图（如果尚未创建）
                    if self.separatorView == nil && self.watermarkSettings != nil {
                        createSeparator(container, settings: self.watermarkSettings!)
                    }
                    
                    if let separator = self.separatorView {
                        // 分隔符高度设置
                        let textHeight: CGFloat
                        if let textLabel = self.textLabel, showText {
                            textHeight = textLabel.bounds.height
                        } else if let preferenceLabel = self.preferenceLabel, showPreference {
                            textHeight = preferenceLabel.bounds.height
                        } else {
                            textHeight = containerHeight * 0.8
                        }
                        
                        // 设置分隔符高度
                        separator.frame = CGRect(
                            x: separator.frame.origin.x,
                            y: separator.frame.origin.y,
                            width: separator.bounds.width,
                            height: textHeight
                        )
                        
                        // 获取分隔符线宽
                        let separatorLineWidth = separator.lineWidth
                        
                        // 设置与分隔符的间距，考虑分隔符线宽
                        let dynamicSpacing = elementSpacing + separatorLineWidth * 0.5
                        
                        // 计算所有元素的总宽度
                        let totalWidth: CGFloat
                        let rightElement: UIView?
                        
                        if let textLabel = self.textLabel, showText {
                            totalWidth = signatureLabel.bounds.width + dynamicSpacing * 2 + separator.bounds.width + textLabel.bounds.width
                            rightElement = textLabel
                        } else if let preferenceLabel = self.preferenceLabel, showPreference {
                            totalWidth = signatureLabel.bounds.width + dynamicSpacing * 2 + separator.bounds.width + preferenceLabel.bounds.width
                            rightElement = preferenceLabel
                        } else {
                            // 安全处理
                            signatureLabel.center = CGPoint(x: containerWidth / 2, y: containerHeight / 2)
                            print("⚠️ 水印14: 署名存在但文字/偏好缺失")
                            return
                        }
                        
                        // 计算整体居中时的起始x坐标
                        let startX = (containerWidth - totalWidth) / 2
                        
                        // 放置署名在左侧
                        signatureLabel.center = CGPoint(
                            x: startX + signatureLabel.bounds.width / 2,
                            y: containerHeight / 2
                        )
                        
                        // 放置分隔符
                        separator.center = CGPoint(
                            x: startX + signatureLabel.bounds.width + dynamicSpacing,
                            y: containerHeight / 2
                        )
                        
                        // 放置右侧元素
                        if let rightElement = rightElement {
                            rightElement.center = CGPoint(
                                x: startX + signatureLabel.bounds.width + dynamicSpacing * 2 + separator.bounds.width + rightElement.bounds.width / 2,
                                y: containerHeight / 2
                            )
                        }
                        
                        print("✅ 水印14两元素布局: 署名在左, 文字/偏好在右, 有分隔符, 整体居中")
                    }
                }
            }
            
        case 3:
            if showSignature && showLogo && (showText || showPreference || showDescription) {
                // 三元素布局：署名在左，Logo和文字/偏好在右侧，中间是分隔符
                // 这段代码从CustomWatermarkStyle11.swift复制修改而来
                
                // 1. 署名放在左侧
                placeElementLeft(self.signatureLabel, padding: leftPadding, containerHeight: containerHeight)
                
                // 2. 计算右侧区域总宽度
                let rightAreaWidth = containerWidth - leftPadding - rightPadding - (self.signatureLabel?.bounds.width ?? 0)
                let rightSideX = containerWidth - rightAreaWidth/2 - rightPadding
                
                // 3. 放置Logo、分隔符和文字/偏好
                if let logoView = self.logoView, let separator = self.separatorView {
                    // 计算右侧元素总宽度
                    let totalRightWidth = logoView.bounds.width + separator.bounds.width + 
                                         (showText ? (self.textLabel?.bounds.width ?? 0) : 
                                         (showDescription ? (self.descriptionLabel?.bounds.width ?? 0) :
                                         (self.preferenceLabel?.bounds.width ?? 0)))
                    
                    // 设置固定间距为1.5%屏幕宽度
                    let spacing = UIScreen.main.bounds.width * 0.015 // 固定间距
                    
                    // 右侧组合物整体靠右，与两元素布局保持一致
                    // 从右向左依次放置：文字/偏好、分隔符、Logo
                    
                    // 文字/描述/偏好放在最右边
                    if let textLabel = self.textLabel, showText {
                        placeElementRight(textLabel, padding: rightPadding, containerHeight: containerHeight, containerWidth: containerWidth)
                        
                        // 获取文字高度
                        let textHeight = textLabel.bounds.height
                        
                        // 估算文本是否为多行
                        let fontSize = textLabel.font.pointSize
                        let estimatedLineHeight = fontSize * 1.3 // 估算的单行高度（考虑行间距）
                        let estimatedLines = textHeight / estimatedLineHeight
                        
                        // 根据行数计算分隔符高度
                        let separatorHeight: CGFloat
                        if estimatedLines > 1.5 { // 若为双行文本
                            separatorHeight = textHeight * 0.8 // 设为文本高度的80%
                            print("✅ 检测到多行文本，分隔符高度设为80%")
                        } else {
                            separatorHeight = textHeight // 单行文本保持一致
                        }
                        
                        // 获取分隔符线宽，用于动态调整间距
                        let separatorLineWidth = separator.lineWidth
                        
                        // 设置动态间距，考虑分隔符线宽 (线宽越大，间距稍微加大)
                        let dynamicSpacing = UIScreen.main.bounds.width * (0.015 + separatorLineWidth * 0.001)
                        
                        // 分隔符放在文字左边
                        let separatorX = textLabel.center.x - textLabel.bounds.width/2 - dynamicSpacing - separator.bounds.width/2
                        
                        // 调整分隔符尺寸 - 宽度保持不变，高度根据文本行数调整
                        separator.frame = CGRect(
                            x: separator.frame.origin.x,
                            y: separator.frame.origin.y,
                            width: separator.bounds.width,
                            height: separatorHeight
                        )
                        
                        // 分隔符垂直居中
                        separator.center = CGPoint(x: separatorX, y: containerHeight/2)
                        
                        // Logo放在分隔符左边，同样使用动态间距
                        let logoX = separatorX - separator.bounds.width/2 - dynamicSpacing - logoView.bounds.width/2
                        logoView.center = CGPoint(x: logoX, y: containerHeight/2)
                        
                        print("✅ 水印14三元素布局: 署名在左, Logo和文字在右(有分隔符), 文本高度=\(textHeight), 分隔符高度=\(separatorHeight), 分隔符线宽=\(separatorLineWidth), 间距=\(dynamicSpacing)")
                    } else if let preferenceLabel = self.preferenceLabel, showPreference {
                        placeElementRight(preferenceLabel, padding: rightPadding, containerHeight: containerHeight, containerWidth: containerWidth)
                        
                        // 获取偏好高度
                        let preferenceHeight = preferenceLabel.bounds.height
                        
                        // 估算文本是否为多行
                        let fontSize = preferenceLabel.font.pointSize
                        let estimatedLineHeight = fontSize * 1.3 // 估算的单行高度（考虑行间距）
                        let estimatedLines = preferenceHeight / estimatedLineHeight
                        
                        // 根据行数计算分隔符高度
                        let separatorHeight: CGFloat
                        if estimatedLines > 1.5 { // 若为双行文本
                            separatorHeight = preferenceHeight * 0.8 // 设为文本高度的80%
                            print("✅ 检测到多行文本，分隔符高度设为80%")
                        } else {
                            separatorHeight = preferenceHeight // 单行文本保持一致
                        }
                        
                        // 获取分隔符线宽，用于动态调整间距
                        let separatorLineWidth = separator.lineWidth
                        
                        // 设置动态间距，考虑分隔符线宽 (线宽越大，间距稍微加大)
                        let dynamicSpacing = UIScreen.main.bounds.width * (0.015 + separatorLineWidth * 0.001)
                        
                        // 分隔符放在偏好左边
                        let separatorX = preferenceLabel.center.x - preferenceLabel.bounds.width/2 - dynamicSpacing - separator.bounds.width/2
                        
                        // 调整分隔符尺寸 - 宽度保持不变，高度根据文本行数调整
                        separator.frame = CGRect(
                            x: separator.frame.origin.x,
                            y: separator.frame.origin.y,
                            width: separator.bounds.width,
                            height: separatorHeight
                        )
                        
                        // 分隔符垂直居中
                        separator.center = CGPoint(x: separatorX, y: containerHeight/2)
                        
                        // Logo放在分隔符左边，同样使用动态间距
                        let logoX = separatorX - separator.bounds.width/2 - dynamicSpacing - logoView.bounds.width/2
                        logoView.center = CGPoint(x: logoX, y: containerHeight/2)
                        
                        print("✅ 水印14三元素布局: 署名在左, Logo和偏好在右(有分隔符), 偏好高度=\(preferenceHeight), 分隔符高度=\(separatorHeight), 分隔符线宽=\(separatorLineWidth), 间距=\(dynamicSpacing)")
                    } else if let descriptionLabel = self.descriptionLabel, showDescription {
                        placeElementRight(descriptionLabel, padding: rightPadding, containerHeight: containerHeight, containerWidth: containerWidth)
                        
                        // 获取描述高度
                        let descriptionHeight = descriptionLabel.bounds.height
                        
                        // 估算文本是否为多行
                        let fontSize = descriptionLabel.font.pointSize
                        let estimatedLineHeight = fontSize * 1.3 // 估算的单行高度（考虑行间距）
                        let estimatedLines = descriptionHeight / estimatedLineHeight
                        
                        // 根据行数计算分隔符高度
                        let separatorHeight: CGFloat
                        if estimatedLines > 1.5 { // 若为双行文本
                            separatorHeight = descriptionHeight * 0.8 // 设为文本高度的80%
                            print("✅ 检测到多行描述文本，分隔符高度设为80%")
                        } else {
                            separatorHeight = descriptionHeight // 单行文本保持一致
                        }
                        
                        // 获取分隔符线宽，用于动态调整间距
                        let separatorLineWidth = separator.lineWidth
                        
                        // 设置动态间距，考虑分隔符线宽 (线宽越大，间距稍微加大)
                        let dynamicSpacing = UIScreen.main.bounds.width * (0.015 + separatorLineWidth * 0.001)
                        
                        // 分隔符放在描述文本左边
                        let separatorX = descriptionLabel.center.x - descriptionLabel.bounds.width/2 - dynamicSpacing - separator.bounds.width/2
                        
                        // 调整分隔符尺寸 - 宽度保持不变，高度根据文本行数调整
                        separator.frame = CGRect(
                            x: separator.frame.origin.x,
                            y: separator.frame.origin.y,
                            width: separator.bounds.width,
                            height: separatorHeight
                        )
                        
                        // 分隔符垂直居中
                        separator.center = CGPoint(x: separatorX, y: containerHeight/2)
                        
                        // Logo放在分隔符左边，同样使用动态间距
                        let logoX = separatorX - separator.bounds.width/2 - dynamicSpacing - logoView.bounds.width/2
                        logoView.center = CGPoint(x: logoX, y: containerHeight/2)
                        
                        print("✅ 水印14三元素布局: 署名在左, Logo和描述在右(有分隔符), 描述高度=\(descriptionHeight), 分隔符高度=\(separatorHeight), 分隔符线宽=\(separatorLineWidth), 间距=\(dynamicSpacing)")
                    }
                }
            }
            
            // 描述作为特殊元素，如果showDescription为真，说明已满足所有显示条件
            if showDescription {
                if let signatureLabel = self.signatureLabel, let descriptionLabel = self.descriptionLabel {
                    // 改为上下布局，间距为0.25%屏幕高度
                    let verticalSpacing = UIScreen.main.bounds.height * 0.0025
                    
                    // 计算总高度
                    let totalHeight = signatureLabel.bounds.height + verticalSpacing + descriptionLabel.bounds.height
                    
                    // 计算上下元素的y坐标
                    let signatureY = containerHeight/2 - totalHeight/2 + signatureLabel.bounds.height/2
                    let descriptionY = signatureY + signatureLabel.bounds.height/2 + verticalSpacing + descriptionLabel.bounds.height/2
                    
                    // 署名放在上方
                    signatureLabel.center = CGPoint(x: signatureLabel.center.x, y: signatureY)
                    
                    // 描述放在下方，x坐标保持与署名相同
                    descriptionLabel.center = CGPoint(x: signatureLabel.center.x, y: descriptionY)
                    
                    print("✅ 水印14: 描述作为特殊元素与署名上下布局，垂直间距=\(verticalSpacing)，为屏幕高度的0.25%")
                }
            }
            
        default:
            print("⚠️ 水印14: 不支持超过3个常规元素的布局，元素可能不可见")
            break
        }
    }
    
    /// 辅助方法：将元素放置在左侧
    private func placeElementLeft(_ element: UIView?, padding: CGFloat, containerHeight: CGFloat) {
        guard let element = element else { return }
        let x = padding + element.bounds.width / 2
        element.center = CGPoint(x: x, y: containerHeight / 2)
    }
    
    /// 辅助方法：将元素放置在右侧
    private func placeElementRight(_ element: UIView?, padding: CGFloat, containerHeight: CGFloat, containerWidth: CGFloat) {
        guard let element = element else { return }
        let x = containerWidth - padding - element.bounds.width / 2
        element.center = CGPoint(x: x, y: containerHeight / 2)
    }

    /// 移除自定义水印14效果
    func remove(from previewContainer: UIView) {
        // 清理水印元素
        self.logoView?.removeFromSuperview()
        self.textLabel?.removeFromSuperview()
        self.descriptionLabel?.removeFromSuperview()
        self.preferenceLabel?.removeFromSuperview()
        self.signatureLabel?.removeFromSuperview()
        self.separatorView?.removeFromSuperview()
        self.bottomItemsContainer?.removeFromSuperview()
        
        guard let wrapperView = self.scaledCustom14FrameWrapper,
              let contentView = self.originalContentView,
              let superview = self.originalSuperview,
              let originalFrame = self.originalFrameInSuperview,
              let originalTransform = self.originalTransform,
              let originalIndex = self.originalIndexInSuperview
        else {
            self.scaledCustom14FrameWrapper?.removeFromSuperview()
            cleanupStoredState()
            return
        }

        contentView.removeFromSuperview()
        contentView.transform = .identity
        contentView.frame = originalFrame
        contentView.transform = originalTransform
        
        if superview.subviews.count > originalIndex {
            superview.insertSubview(contentView, at: originalIndex)
        } else {
            superview.addSubview(contentView)
        }
        
        wrapperView.removeFromSuperview()
        cleanupStoredState()
        print("✅ CustomWatermarkStyle14: 已移除自定义水印14。")
    }

    /// 辅助方法：恢复内容视图原始状态（用于apply失败时）
    private func restoreOriginalContentViewState() {
        guard let contentView = self.originalContentView,
              let superview = self.originalSuperview,
              let originalFrame = self.originalFrameInSuperview,
              let originalTransform = self.originalTransform,
              let originalIndex = self.originalIndexInSuperview else {
            cleanupStoredState() // 即使部分信息缺失，也尝试清理
            return
        }
        contentView.removeFromSuperview()
        contentView.transform = .identity
        contentView.frame = originalFrame
        contentView.transform = originalTransform
        if superview.subviews.count > originalIndex {
            superview.insertSubview(contentView, at: originalIndex)
        } else {
            superview.addSubview(contentView)
        }
        cleanupStoredState()
    }

    /// 辅助方法：清理存储的状态变量
    private func cleanupStoredState() {
        self.originalContentView = nil
        self.originalFrameInSuperview = nil
        self.originalTransform = nil
        self.originalSuperview = nil
        self.originalIndexInSuperview = nil
        self.scaledCustom14FrameWrapper = nil
        self.logoView = nil
        self.textLabel = nil
        self.descriptionLabel = nil
        self.preferenceLabel = nil
        self.signatureLabel = nil
        self.separatorView = nil
        self.bottomItemsContainer = nil
    }
} 