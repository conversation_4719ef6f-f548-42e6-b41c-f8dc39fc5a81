import UIKit

// MARK: - 自定义水印25
/// 自定义水印样式25 - 基于拍立得风格水印（自定义水印2）
class CustomWatermarkStyle25: WatermarkStyle {
    /// 上边框宽度
    private let topBorderWidth: CGFloat
    
    /// 底部边框宽度
    private let bottomBorderWidth: CGFloat
    
    /// 左右边框宽度
    private let sideBorderWidth: CGFloat
    
    /// 边框颜色
    private let borderColor: UIColor
    
    // --- 存储原始状态和中间视图 ---
    private weak var originalContentView: UIView?
    private var originalFrameInSuperview: CGRect?
    private var originalTransform: CGAffineTransform?
    private weak var originalSuperview: UIView?
    private var originalIndexInSuperview: Int?
    private weak var scaledCustom25FrameWrapper: UIView? // 用于存储最外层的、已缩放的自定义25框架视图
    private weak var blurEffectBackgroundView: UIVisualEffectView? // 用于存储模糊效果视图
    
    // --- 水印元素相关 ---
    private weak var bottomItemsContainer: UIView? // 底部边框中的元素容器
    private weak var logoView: UIImageView? // Logo图片视图
    private weak var textLabel: UILabel? // 水印文字标签
    private weak var preferenceLabel: UILabel? // 偏好选项标签
    private var watermarkSettings: WatermarkSettings? // 保存水印设置的引用

    /// 初始化
    /// - Parameters:
    ///   - topBorderWidth: 上边框宽度
    ///   - bottomBorderWidth: 底部边框宽度
    ///   - sideBorderWidth: 左右边框宽度
    ///   - borderColor: 边框颜色，默认为白色
    init(topBorderWidth: CGFloat,
         bottomBorderWidth: CGFloat,
         sideBorderWidth: CGFloat,
         borderColor: UIColor = .white) {
        self.topBorderWidth = topBorderWidth
        self.bottomBorderWidth = bottomBorderWidth
        self.sideBorderWidth = sideBorderWidth
        self.borderColor = borderColor
    }
    
    /// 查找实际的内容视图 (与BorderWatermarkStyle中的方法类似)
    private func findActualContentView(in container: UIView) -> UIView? {
        if let imageView = container.subviews.first(where: { $0 is UIImageView }) {
            return imageView
        }
        if let intermediateContainer = container.viewWithTag(100) {
            if let cameraPreview = intermediateContainer.viewWithTag(101) {
                return cameraPreview
            }
        }
        if container is UIImageView || container.tag == 101 {
             return container
        }
        print("⚠️ CustomWatermarkStyle25: 未找到实际的内容视图。Container: \(container)")
        return container.subviews.first
    }

    // 使用公共扩展中的捕获视图方法
    
    /// 应用自定义水印25样式到预览视图 - 修改模糊背景实现部分
    func apply(to previewContainer: UIView) {
        if scaledCustom25FrameWrapper != nil {
            remove(from: previewContainer)
        }

        guard let contentView = findActualContentView(in: previewContainer) else {
            print("❌ CustomWatermarkStyle25: apply - 无法找到实际内容视图。")
            return
        }

        // 获取水印设置
        self.watermarkSettings = WatermarkSettingsManager.shared.getSettings()
        // 获取模糊边框设置
        let isBlurBorderEnabled = watermarkSettings?.isBlurBorderEnabled ?? false
        let blurIntensity = watermarkSettings?.blurIntensity ?? 0.7
        // 获取阴影效果设置
        let isShadowEnabled = watermarkSettings?.isShadowEnabled ?? false

        // 1. 保存原始状态
        self.originalContentView = contentView
        self.originalTransform = contentView.transform
        self.originalSuperview = contentView.superview
        if let superview = contentView.superview {
            self.originalFrameInSuperview = contentView.frame
            self.originalIndexInSuperview = superview.subviews.firstIndex(of: contentView)
        } else {
            self.originalFrameInSuperview = contentView.bounds
            self.originalIndexInSuperview = 0
        }

        // --- 创建包含 contentView 和不对称边框的视图 (`custom25Wrapper`) ---
        let contentOriginalSize = contentView.bounds.size
        
        let custom25WrapperWidth = contentOriginalSize.width + (2 * self.sideBorderWidth)
        let custom25WrapperHeight = contentOriginalSize.height + self.topBorderWidth + self.bottomBorderWidth
        let custom25WrapperSize = CGSize(width: custom25WrapperWidth, height: custom25WrapperHeight)

        let custom25Wrapper = UIView(frame: CGRect(origin: .zero, size: custom25WrapperSize))
        custom25Wrapper.clipsToBounds = false // 允许子视图的阴影溢出
        
        // 根据模糊边框设置决定背景 - 两种模式完全互斥
        if isBlurBorderEnabled {
            // 模糊边框模式 - 背景透明
            custom25Wrapper.backgroundColor = .clear
            
            // 使用通用方法捕获内容视图并应用模糊效果
            if let capturedImage = WatermarkStyleUtils.captureView(contentView) {
                WatermarkStyleUtils.applyBlurBackground(to: custom25Wrapper, with: capturedImage, intensity: blurIntensity, settings: watermarkSettings)
            } else {
                // 截图失败时回退到默认背景
                print("⚠️ CustomWatermarkStyle25: 无法获取视图截图，使用默认背景")
                custom25Wrapper.backgroundColor = self.borderColor
            }
        } else {
            // 白色边框模式 - 只设置背景颜色
            custom25Wrapper.backgroundColor = self.borderColor
        }
        
        contentView.transform = .identity
        contentView.frame = CGRect(x: self.sideBorderWidth, y: self.topBorderWidth, width: contentOriginalSize.width, height: contentOriginalSize.height)
        custom25Wrapper.addSubview(contentView)
        
        // 使用扩展方法应用阴影效果
        WatermarkStyleUtils.applyShadowEffect(to: contentView, in: custom25Wrapper, isShadowEnabled: isShadowEnabled, styleIdentifier: "CustomWatermarkStyle25")

        // --- 创建底部元素容器视图 ---
        let bottomItemsContainerFrame = CGRect(
            x: self.sideBorderWidth,
            y: self.topBorderWidth + contentOriginalSize.height,
            width: contentOriginalSize.width,
            height: self.bottomBorderWidth
        )
        
        let bottomContainer = UIView(frame: bottomItemsContainerFrame)
        bottomContainer.backgroundColor = .clear // 透明背景
        custom25Wrapper.addSubview(bottomContainer)
        self.bottomItemsContainer = bottomContainer
        
        // 添加水印元素到底部容器
        addWatermarkElementsToBottomContainer(bottomContainer)

        self.scaledCustom25FrameWrapper = custom25Wrapper
        previewContainer.addSubview(custom25Wrapper)

        // --- 将 `custom25Wrapper` (作为整体) 缩放并居中到 `previewContainer` ---
        let containerSize = previewContainer.bounds.size
        guard custom25Wrapper.bounds.width > 0, custom25Wrapper.bounds.height > 0 else {
            print("❌ CustomWatermarkStyle25: custom25Wrapper 尺寸为0。")
            custom25Wrapper.removeFromSuperview()
            restoreOriginalContentViewState() // 尝试恢复
            return
        }

        let scaleX = containerSize.width / custom25Wrapper.bounds.width
        let scaleY = containerSize.height / custom25Wrapper.bounds.height
        let finalScaleFactor = min(scaleX, scaleY)

        custom25Wrapper.transform = CGAffineTransform(scaleX: finalScaleFactor, y: finalScaleFactor)
        custom25Wrapper.center = CGPoint(x: containerSize.width / 2, y: containerSize.height / 2)
        
        print("✅ CustomWatermarkStyle25: 已应用自定义水印25边框。模糊效果: \(isBlurBorderEnabled ? "已启用" : "未启用")。最终缩放: \(finalScaleFactor)")
    }
    
    // 使用公共扩展中的应用模糊背景方法
    
    /// 添加水印元素到底部容器
    private func addWatermarkElementsToBottomContainer(_ container: UIView) {
        guard let settings = self.watermarkSettings else { return }
        
        // 确定需要显示哪些元素
        let showLogo = !settings.selectedLogo.isEmpty // Logo不为空字符串表示启用
        // MODIFIED: "Text" for Polaroid is now driven by Signature settings
        let showText = settings.isWatermarkSignatureEnabled && !settings.watermarkSignature.isEmpty // Main text (from signature) enabled and not empty
        // 检查新格式或旧格式
        let showPreference = !settings.selectedPreferences.isEmpty || settings.preferenceOption != "OFF" // 存在选中的偏好选项或旧格式不为OFF
        
        // 统计启用的元素数量
        let enabledElementsCount = [showLogo, showText, showPreference].filter { $0 }.count
        
        // 如果没有任何元素启用，直接返回
        if enabledElementsCount == 0 { return }
        
        let containerHeight = container.bounds.height
        let containerWidth = container.bounds.width
        
        // 创建内容视图
        let contentStackView = UIStackView()
        contentStackView.axis = .vertical
        contentStackView.alignment = .center
        
        // 使用常量替换间距设置
        contentStackView.spacing = UIScreen.main.bounds.height * WatermarkConstants.Custom15.stackViewSpacingFactor
        
        contentStackView.backgroundColor = .clear
        contentStackView.translatesAutoresizingMaskIntoConstraints = false
        container.addSubview(contentStackView)
        
        // 添加Logo（如果需要）
        if showLogo {
            createLogoView(container, settings: settings, enabledElementsCount: enabledElementsCount)
        }
        
        // MODIFIED: This creates the main text element, which is now driven by signature settings
        if showText {
            createMainTextAsSignatureLabel(container, settings: settings, enabledElementsCount: enabledElementsCount)
        }
        
        if showPreference {
            createPreferenceLabel(container, settings: settings, enabledElementsCount: enabledElementsCount)
        }
        
        // 根据启用的元素数量，安排布局
        arrangeElementsLayout(container, enabledCount: enabledElementsCount)
    }
    
    /// 创建Logo视图
    private func createLogoView(_ container: UIView, settings: WatermarkSettings, enabledElementsCount: Int) {
        // 水印25使用线性增长 - Logo也使用线性增长（50%增长）
        let baseSize: CGFloat
        let growthFactor: CGFloat
        
        if enabledElementsCount == 1 {
            baseSize = WatermarkConstants.Custom15.logoSingleElementBaseSize
            growthFactor = WatermarkConstants.Custom15.logoSingleElementGrowthFactor
        } else if enabledElementsCount == 2 {
            baseSize = WatermarkConstants.Custom15.logoTwoElementsBaseSize
            growthFactor = WatermarkConstants.Custom15.logoTwoElementsGrowthFactor
        } else {
            baseSize = WatermarkConstants.Custom15.logoThreeElementsBaseSize
            growthFactor = WatermarkConstants.Custom15.logoThreeElementsGrowthFactor
        }
        
        // 线性增长公式：logoSize = baseSize + (currentBorderWidth - baseBorderWidth) * growthFactor
        let baseBorderWidth = UIScreen.main.bounds.height * WatermarkConstants.Custom15.baseBorderHeight
        let borderWidthDifference = self.bottomBorderWidth - baseBorderWidth
        let logoSize = UIScreen.main.bounds.height * (baseSize + borderWidthDifference / UIScreen.main.bounds.height * growthFactor)
        
        let logoImageView = LogoCreator.createLogo(
            with: settings,
            fixedSize: logoSize
        )
        
        container.addSubview(logoImageView)
        self.logoView = logoImageView
        
        print("✅ 水印25 Logo动态大小: 元素数量=\(enabledElementsCount), logoSize=\((logoSize/UIScreen.main.bounds.height)*100)%")
    }
    
    /// 创建文字标签 (MODIFIED: Now creates main text based on Signature settings)
    private func createMainTextAsSignatureLabel(_ container: UIView, settings: WatermarkSettings, enabledElementsCount: Int) {
        // 水印25使用线性增长 - 文字只有25%增长，而Logo有50%增长
        let baseSize: CGFloat
        let growthFactor: CGFloat
        
        if enabledElementsCount == 1 {
            // 对于单个元素，保持原有的大小计算
            baseSize = WatermarkConstants.Custom15.fontSingleElementBaseSize
            growthFactor = WatermarkConstants.Custom15.fontSingleElementGrowthFactor
        } else {
            // 对于两个或三个元素，使用相同的大小设置
            // 统一使用两个元素的大小设置
            baseSize = WatermarkConstants.Custom15.fontTwoElementsBaseSize
            growthFactor = WatermarkConstants.Custom15.fontTwoElementsGrowthFactor
        }
        
        // 线性增长公式：fontSize = baseSize + (currentBorderWidth - baseBorderWidth) * growthFactor
        let baseBorderWidth = UIScreen.main.bounds.height * WatermarkConstants.Custom15.baseBorderHeight
        let borderWidthDifference = self.bottomBorderWidth - baseBorderWidth
        var fontSize = UIScreen.main.bounds.height * (baseSize + borderWidthDifference / UIScreen.main.bounds.height * growthFactor)
        // 应用署名大小乘数
        fontSize *= CGFloat(settings.signatureFontSizeMultiplier) // <<<< APPLY MULTIPLIER
        
        // MODIFIED: Create label using signature text and apply font style as signature
        let label = UILabel()
        label.text = settings.watermarkSignature // Use signature text
        label.textAlignment = .center
        
        // Apply font style, marking as signature for correct thickness application
        WatermarkStyleUtils.applyFontStyle(to: label, with: settings, fontSize: fontSize, styleIdentifier: "Custom25.MainTextAsSignature", isSignature: true)
        
        // 设置尺寸
        let maxWidth = container.bounds.width * WatermarkConstants.Custom15.labelMaxWidthContainerWidthFactor
        let size = label.sizeThatFits(CGSize(width: maxWidth, height: .greatestFiniteMagnitude))
        label.frame.size = size
        
        container.addSubview(label)
        self.textLabel = label // Still store in textLabel, but it's functionally the signature
        
        print("✅ 水印25 主文本(源自署名)动态大小: 元素数量=\(enabledElementsCount), fontSize=\((fontSize/UIScreen.main.bounds.height)*100)%")
    }
    
    /// 创建偏好标签
    private func createPreferenceLabel(_ container: UIView, settings: WatermarkSettings, enabledElementsCount: Int) {
        // 水印25使用线性增长 - 偏好标签与文字使用相同的增长模式（25%增长）
        let baseSize: CGFloat
        let growthFactor: CGFloat
        
        if enabledElementsCount == 1 {
            baseSize = WatermarkConstants.Custom15.fontSingleElementBaseSize
            growthFactor = WatermarkConstants.Custom15.fontSingleElementGrowthFactor
        } else if enabledElementsCount == 2 {
            baseSize = WatermarkConstants.Custom15.fontTwoElementsBaseSize
            growthFactor = WatermarkConstants.Custom15.fontTwoElementsGrowthFactor
        } else {
            baseSize = WatermarkConstants.Custom15.fontThreeElementsBaseSize
            growthFactor = WatermarkConstants.Custom15.fontThreeElementsGrowthFactor
        }
        
        // 线性增长公式：fontSize = baseSize + (currentBorderWidth - baseBorderWidth) * growthFactor
        let baseBorderWidth = UIScreen.main.bounds.height * WatermarkConstants.Custom15.baseBorderHeight
        let borderWidthDifference = self.bottomBorderWidth - baseBorderWidth
        let fontSize = UIScreen.main.bounds.height * (baseSize + borderWidthDifference / UIScreen.main.bounds.height * growthFactor)
        
        let label = PreferenceLabelCreator.createLabel(
            for: settings.preferenceOption, 
            with: settings, 
            enabledElementsCount: enabledElementsCount,
            fixedFontSize: fontSize
        )
        
        // 设置尺寸
        let maxWidth = container.bounds.width * WatermarkConstants.Custom15.labelMaxWidthContainerWidthFactor
        let size = label.sizeThatFits(CGSize(width: maxWidth, height: .greatestFiniteMagnitude))
        label.frame.size = size
        
        container.addSubview(label)
        self.preferenceLabel = label
        
        print("✅ 水印25 偏好动态大小: 元素数量=\(enabledElementsCount), fontSize=\((fontSize/UIScreen.main.bounds.height)*100)%")
    }
    
    /// 根据元素数量安排布局
    private func arrangeElementsLayout(_ container: UIView, enabledCount: Int) {
        let containerHeight = container.bounds.height
        let containerWidth = container.bounds.width
        
        switch enabledCount {
        case 1:
            // 只有一个元素：居中显示
            if let logoView = self.logoView {
                // Logo垂直居中
                logoView.center = CGPoint(x: containerWidth / 2, y: containerHeight / 2)
            } else if let textLabel = self.textLabel {
                // 文字垂直居中
                textLabel.center = CGPoint(x: containerWidth / 2, y: containerHeight / 2)
            } else if let preferenceLabel = self.preferenceLabel {
                // 偏好垂直居中
                preferenceLabel.center = CGPoint(x: containerWidth / 2, y: containerHeight / 2)
            }
            
        case 2:
            // 两个元素：保持动态间距，整体居中显示
            // 动态计算间距：baseSize + (borderWidthDifference * growthFactor)
            let baseBorderWidth = UIScreen.main.bounds.height * WatermarkConstants.Custom15.baseBorderHeight
            let borderWidthDifference = self.bottomBorderWidth - baseBorderWidth
            let spacingFactor = WatermarkConstants.Custom15.spacing2ElementsBaseSize + borderWidthDifference / UIScreen.main.bounds.height * WatermarkConstants.Custom15.spacing2ElementsGrowthFactor
            let elementSpacing = UIScreen.main.bounds.height * spacingFactor

            // 获取元素
            var firstElement: UIView?
            var secondElement: UIView?
            
            // 确定元素顺序（Logo优先在上方）
            if let logoView = self.logoView {
                firstElement = logoView
                if let textLabel = self.textLabel {
                    secondElement = textLabel
                } else if let preferenceLabel = self.preferenceLabel {
                    secondElement = preferenceLabel
                }
            } else if let textLabel = self.textLabel, let preferenceLabel = self.preferenceLabel {
                // 没有Logo时，文字在上，偏好在下
                firstElement = textLabel
                secondElement = preferenceLabel
            }
            
            guard let first = firstElement, let second = secondElement else { return }
            
            // 计算两个元素的总高度（包括间距）
            let totalHeight = first.bounds.height + elementSpacing + second.bounds.height
            
            // 计算起始Y位置，使整体居中
            let startY = (containerHeight - totalHeight) / 2
            
            // 设置第一个元素位置
            let firstY = startY + (first.bounds.height / 2)
            first.center = CGPoint(x: containerWidth / 2, y: firstY)
            
            // 设置第二个元素位置
            let secondY = firstY + (first.bounds.height / 2) + elementSpacing + (second.bounds.height / 2)
            second.center = CGPoint(x: containerWidth / 2, y: secondY)
            
            print("✅ 水印25两元素居中布局: 第一元素Y=\(firstY), 第二元素Y=\(secondY), 动态间距=\((spacingFactor*100))%, 总高度=\(totalHeight)")
            
        case 3:
            // 三个元素：Logo和署名水平排布，偏好在下方
            // 动态计算间距：baseSize + (borderWidthDifference * growthFactor)
            let baseBorderWidth = UIScreen.main.bounds.height * WatermarkConstants.Custom15.baseBorderHeight
            let borderWidthDifference = self.bottomBorderWidth - baseBorderWidth
            let spacingFactor = WatermarkConstants.Custom15.spacing3ElementsBaseSize + borderWidthDifference / UIScreen.main.bounds.height * WatermarkConstants.Custom15.spacing3ElementsGrowthFactor
            let verticalSpacing = UIScreen.main.bounds.height * spacingFactor
            
            guard let logoView = self.logoView,
                  let mainTextLabel = self.textLabel, // This is now the signature-driven text
                  let preferenceLabel = self.preferenceLabel else {
                return
            }
            
            // 固定间距布局方法（与两元素布局保持一致）：
            // 1. 计算元素总高度
            // 2. 垂直居中整体布局
            // 3. 计算各元素精确位置
            
            // 水平间距固定为屏幕宽度的3%
            let horizontalSpacing = UIScreen.main.bounds.width * 0.03
            
            // 1. 水平排布Logo和署名
            // 计算Logo和署名的总宽度
            let topRowTotalWidth = logoView.frame.width + horizontalSpacing + mainTextLabel.frame.width
            let topRowStartX = (containerWidth - topRowTotalWidth) / 2
            
            // 计算上排两个元素的高度(取最大值)
            let topRowHeight = max(logoView.frame.height, mainTextLabel.frame.height)
            
            // 计算所有元素的总高度（包括垂直间距）
            let totalHeight = topRowHeight + verticalSpacing + preferenceLabel.frame.height
            
            // 计算起始Y位置，使整体居中
            let startY = (containerHeight - totalHeight) / 2
            
            // 计算第一行的Y位置
            let topRowY = startY + (topRowHeight / 2)
            
            // 放置Logo（左侧）
            logoView.center = CGPoint(
                x: topRowStartX + logoView.frame.width / 2, 
                y: topRowY
            )
            
            // 放置署名文本（右侧）
            mainTextLabel.center = CGPoint(
                x: topRowStartX + logoView.frame.width + horizontalSpacing + mainTextLabel.frame.width / 2, 
                y: topRowY
            )
            
            // 2. 放置偏好在下方
            let bottomRowY = topRowY + (topRowHeight / 2) + verticalSpacing + (preferenceLabel.frame.height / 2)
            preferenceLabel.center = CGPoint(
                x: containerWidth / 2, 
                y: bottomRowY
            )
            
            print("✅ 水印25三元素布局: Logo和署名水平排布，偏好在下方。水平间距=\((horizontalSpacing/UIScreen.main.bounds.width)*100)%")
            
        default:
            break
        }
    }
    
    /// 移除自定义水印25样式效果
    func remove(from previewContainer: UIView) {
        // 先移除可能的模糊效果视图引用
        self.blurEffectBackgroundView = nil
        
        // 清理水印元素
        self.logoView?.removeFromSuperview()
        self.textLabel?.removeFromSuperview()
        self.preferenceLabel?.removeFromSuperview()
        self.bottomItemsContainer?.removeFromSuperview()
        
        guard let custom25Wrapper = self.scaledCustom25FrameWrapper,
              let contentView = self.originalContentView,
              let superview = self.originalSuperview,
              let originalFrame = self.originalFrameInSuperview,
              let originalTransform = self.originalTransform,
              let originalIndex = self.originalIndexInSuperview
        else {
            self.scaledCustom25FrameWrapper?.removeFromSuperview()
            cleanupStoredState()
            return
        }

        contentView.removeFromSuperview()
        contentView.transform = .identity
        contentView.frame = originalFrame
        contentView.transform = originalTransform
        
        if superview.subviews.count > originalIndex {
            superview.insertSubview(contentView, at: originalIndex)
        } else {
            superview.addSubview(contentView)
        }
        
        custom25Wrapper.removeFromSuperview()
        cleanupStoredState()
        print("✅ CustomWatermarkStyle25: 已移除自定义水印25边框。")
    }

    /// 辅助方法：恢复内容视图原始状态（用于apply失败时）
    private func restoreOriginalContentViewState() {
        guard let contentView = self.originalContentView,
              let superview = self.originalSuperview,
              let originalFrame = self.originalFrameInSuperview,
              let originalTransform = self.originalTransform,
              let originalIndex = self.originalIndexInSuperview else {
            cleanupStoredState() // 即使部分信息缺失，也尝试清理
            return
        }
        contentView.removeFromSuperview()
        contentView.transform = .identity
        contentView.frame = originalFrame
        contentView.transform = originalTransform
        if superview.subviews.count > originalIndex {
            superview.insertSubview(contentView, at: originalIndex)
        } else {
            superview.addSubview(contentView)
        }
    }
    
    /// 辅助方法：清理存储状态
    private func cleanupStoredState() {
        originalContentView = nil
        originalFrameInSuperview = nil
        originalTransform = nil
        originalSuperview = nil
        originalIndexInSuperview = nil
        scaledCustom25FrameWrapper = nil
        bottomItemsContainer = nil
        logoView = nil
        textLabel = nil
        preferenceLabel = nil
        watermarkSettings = nil
    }
} 