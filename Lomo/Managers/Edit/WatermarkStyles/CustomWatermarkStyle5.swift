import UIKit

// MARK: - 自定义水印5
/// 自定义水印5 - 三边边框宽度一致，一边宽边框（位置可配置）
class CustomWatermarkStyle5: WatermarkStyle {
    /// 边框位置枚举
    enum WideBorderPosition {
        case left
        case right
    }
    
    /// 上边框宽度
    private let topBorderWidth: CGFloat
    
    /// 底部边框宽度
    private let bottomBorderWidth: CGFloat
    
    /// 右边框宽度
    private let rightBorderWidth: CGFloat
    
    /// 左边框宽度
    private let leftBorderWidth: CGFloat
    
    /// 宽边框位置
    private let wideBorderPosition: WideBorderPosition
    
    /// 边框颜色
    private let borderColor: UIColor
    
    // --- 存储原始状态和中间视图 ---
    private weak var originalContentView: UIView?
    private var originalFrameInSuperview: CGRect?
    private var originalTransform: CGAffineTransform?
    private weak var originalSuperview: UIView?
    private var originalIndexInSuperview: Int?
    private weak var scaledFrameWrapper: UIView? // 用于存储最外层的已缩放框架视图
    
    // --- 水印元素相关 ---
    private weak var itemsContainer: UIView? // 宽边框中的元素容器
    private weak var logoView: UIImageView? // Logo图片视图
    private weak var textLabel: UILabel? // 水印文字标签
    private weak var preferenceLabel: UILabel? // 偏好选项标签
    private weak var signatureLabel: UILabel? // 署名标签
    private var watermarkSettings: WatermarkSettings? // 保存水印设置的引用

    /// 初始化
    /// - Parameters:
    ///   - normalBorderWidth: 常规边框宽度
    ///   - wideBorderWidth: 宽边框宽度
    ///   - wideBorderPosition: 宽边框位置
    ///   - borderColor: 边框颜色，默认为白色
    init(normalBorderWidth: CGFloat, 
         wideBorderWidth: CGFloat, // From WatermarkConstants.Custom5.leftBorderScreenHeightFactor * screenHeight
         wideBorderPosition: WideBorderPosition = .left,
         borderColor: UIColor = .white) {
        self.topBorderWidth = normalBorderWidth
        self.bottomBorderWidth = normalBorderWidth
        self.wideBorderPosition = wideBorderPosition
        
        // 根据宽边框位置决定左右边框宽度
        if wideBorderPosition == .left {
            self.leftBorderWidth = wideBorderWidth
        self.rightBorderWidth = normalBorderWidth
        } else {
            self.leftBorderWidth = normalBorderWidth
            self.rightBorderWidth = wideBorderWidth
        }
        
        self.borderColor = borderColor
    }
    
    /// 查找实际的内容视图 (与BorderWatermarkStyle中的方法类似)
    private func findActualContentView(in container: UIView) -> UIView? {
        if let imageView = container.subviews.first(where: { $0 is UIImageView }) {
            return imageView
        }
        if let intermediateContainer = container.viewWithTag(100) {
            if let cameraPreview = intermediateContainer.viewWithTag(101) {
                return cameraPreview
            }
        }
        if container is UIImageView || container.tag == 101 {
             return container
        }
        print("⚠️ CustomWatermarkStyle5: 未找到实际的内容视图。Container: \(container)")
        return container.subviews.first
    }

    /// 应用自定义水印5到预览视图
    func apply(to previewContainer: UIView) {
        if scaledFrameWrapper != nil {
            remove(from: previewContainer)
        }

        guard let contentView = findActualContentView(in: previewContainer) else {
            print("❌ CustomWatermarkStyle5: apply - 无法找到实际内容视图。")
            return
        }

        // 获取水印设置
        self.watermarkSettings = WatermarkSettingsManager.shared.getSettings()
        // 获取模糊边框设置
        let isBlurBorderEnabled = watermarkSettings?.isBlurBorderEnabled ?? false
        let blurIntensity = watermarkSettings?.blurIntensity ?? 0.7
        // 获取阴影效果设置
        let isShadowEnabled = watermarkSettings?.isShadowEnabled ?? false

        // 1. 保存原始状态
        self.originalContentView = contentView
        self.originalTransform = contentView.transform
        self.originalSuperview = contentView.superview
        if let superview = contentView.superview {
            self.originalFrameInSuperview = contentView.frame
            self.originalIndexInSuperview = superview.subviews.firstIndex(of: contentView)
        } else {
            self.originalFrameInSuperview = contentView.bounds
            self.originalIndexInSuperview = 0
        }

        // --- 创建包含 contentView 和不对称边框的视图 (`wrapper`) ---
        let contentOriginalSize = contentView.bounds.size
        
        let wrapperWidth = contentOriginalSize.width + self.leftBorderWidth + self.rightBorderWidth
        let wrapperHeight = contentOriginalSize.height + self.topBorderWidth + self.bottomBorderWidth
        let wrapperSize = CGSize(width: wrapperWidth, height: wrapperHeight)

        let wrapper = UIView(frame: CGRect(origin: .zero, size: wrapperSize))
        wrapper.clipsToBounds = false // 允许子视图的阴影溢出
        
        // 根据模糊边框设置决定背景 - 两种模式完全互斥
        if isBlurBorderEnabled {
            // 模糊边框模式 - 背景透明
            wrapper.backgroundColor = .clear
            
            // 使用通用方法捕获内容视图并应用模糊效果
            if let capturedImage = WatermarkStyleUtils.captureView(contentView) {
                WatermarkStyleUtils.applyBlurBackground(to: wrapper, with: capturedImage, intensity: blurIntensity, settings: watermarkSettings)
            } else {
                // 截图失败时回退到默认背景
                print("⚠️ CustomWatermarkStyle5: 无法获取视图截图，使用默认背景")
                wrapper.backgroundColor = self.borderColor
            }
        } else {
            // 普通边框模式 - 设置背景颜色
            wrapper.backgroundColor = self.borderColor
        }
        
        contentView.transform = .identity
        contentView.frame = CGRect(x: self.leftBorderWidth, y: self.topBorderWidth, width: contentOriginalSize.width, height: contentOriginalSize.height)
        wrapper.addSubview(contentView)
        
        // 使用扩展方法应用阴影效果
        WatermarkStyleUtils.applyShadowEffect(to: contentView, in: wrapper, isShadowEnabled: isShadowEnabled, styleIdentifier: "CustomWatermarkStyle5")

        // --- 创建宽边框元素容器视图（根据宽边框位置） ---
        let itemsContainerFrame: CGRect
        
        if self.wideBorderPosition == .left {
            // 宽边框在左侧
            itemsContainerFrame = CGRect(
            x: 0,
            y: self.topBorderWidth,
            width: self.leftBorderWidth,
            height: contentOriginalSize.height
        )
        } else {
            // 宽边框在右侧
            itemsContainerFrame = CGRect(
                x: self.leftBorderWidth + contentOriginalSize.width,
                y: self.topBorderWidth,
                width: self.rightBorderWidth,
                height: contentOriginalSize.height
            )
        }
        
        let itemsContainer = UIView(frame: itemsContainerFrame)
        itemsContainer.backgroundColor = .clear // 透明背景，因为wrapper已经提供了背景
        wrapper.addSubview(itemsContainer)
        self.itemsContainer = itemsContainer
        
        // 添加水印元素到宽边框容器
        addWatermarkElementsToContainer(itemsContainer)

        self.scaledFrameWrapper = wrapper
        previewContainer.addSubview(wrapper)

        // --- 将 `wrapper` (作为整体) 缩放并居中到 `previewContainer` ---
        let containerSize = previewContainer.bounds.size
        guard wrapper.bounds.width > 0, wrapper.bounds.height > 0 else {
            print("❌ CustomWatermarkStyle5: wrapper 尺寸为0。")
            wrapper.removeFromSuperview()
            restoreOriginalContentViewState() // 尝试恢复
            return
        }

        let scaleX = containerSize.width / wrapper.bounds.width
        let scaleY = containerSize.height / wrapper.bounds.height
        let finalScaleFactor = min(scaleX, scaleY)

        wrapper.transform = CGAffineTransform(scaleX: finalScaleFactor, y: finalScaleFactor)
        wrapper.center = CGPoint(x: containerSize.width / 2, y: containerSize.height / 2)
        
        print("✅ CustomWatermarkStyle5: 已应用自定义水印5。最终缩放: \(finalScaleFactor)")
    }
    
    /// 添加水印元素到宽边框容器
    private func addWatermarkElementsToContainer(_ container: UIView) {
        guard let settings = self.watermarkSettings else { return }
        
        // 确定需要显示哪些元素
        let showLogo = !settings.selectedLogo.isEmpty // Logo不为空字符串表示启用
        let showText = settings.isWatermarkTextEnabled && !settings.watermarkText.isEmpty // 文字启用且不为空
        // 检查新格式或旧格式
        let showPreference = !settings.selectedPreferences.isEmpty || settings.preferenceOption != "OFF" // 存在选中的偏好选项或旧格式不为OFF
        let showSignature = settings.isWatermarkSignatureEnabled && !settings.watermarkSignature.isEmpty // 署名启用且不为空
        
        // 统计启用的元素数量
        let enabledElementsCount = [showLogo, showText, showPreference, showSignature].filter { $0 }.count
        
        // 如果没有任何元素启用，直接返回
        if enabledElementsCount == 0 { return }
        
        let containerHeight = container.bounds.height
        let containerWidth = container.bounds.width
        
        // 创建内容视图
        let contentStackView = UIStackView()
        contentStackView.axis = .vertical
        contentStackView.alignment = .center
        
        // 根据显示的元素设置不同的间距
        contentStackView.spacing = UIScreen.main.bounds.height * WatermarkConstants.Custom5.stackViewSpacingScreenHeightFactor // 使用常量
        
        contentStackView.backgroundColor = .clear
        contentStackView.translatesAutoresizingMaskIntoConstraints = false
        container.addSubview(contentStackView)
        
        // 添加Logo（如果需要）
        if showLogo {
            createLogoView(container, settings: settings, enabledElementsCount: enabledElementsCount)
        }
        
        // 添加署名（如果需要）
        if showSignature {
            createSignatureLabel(container, settings: settings, enabledElementsCount: enabledElementsCount)
        }
        
        if showText {
            createTextLabel(container, settings: settings, enabledElementsCount: enabledElementsCount)
        }
        
        if showPreference {
            createPreferenceLabel(container, settings: settings, enabledElementsCount: enabledElementsCount)
        }
        
        // 根据启用的元素数量，安排布局
        arrangeElementsLayout(container, enabledCount: enabledElementsCount)
    }
    
    /// 创建Logo视图
    private func createLogoView(_ container: UIView, settings: WatermarkSettings, enabledElementsCount: Int) {
        // 使用LogoCreator创建Logo
        let factors = (
            single: WatermarkConstants.Custom5.logoSizeSingleElementFactor,
            two: WatermarkConstants.Custom5.logoSizeTwoElementsFactor,
            three: WatermarkConstants.Custom5.logoSizeThreeElementsFactor
        )
        
        let logoImageView = LogoCreator.createLogo(
            with: settings,
            enabledElementsCount: enabledElementsCount,
            customSizeFactors: factors
        )
        
        container.addSubview(logoImageView)
        self.logoView = logoImageView
    }
    
    /// 创建文字标签
    private func createTextLabel(_ container: UIView, settings: WatermarkSettings, enabledElementsCount: Int) {
        // 使用TextLabelCreator创建文本标签
        let factors = (
            single: WatermarkConstants.Custom5.fontSingleElementFactor, 
            two: WatermarkConstants.Custom5.fontTwoElementsFactor, 
            three: WatermarkConstants.Custom5.fontThreeElementsFactor
        )
        
        let label = TextLabelCreator.createLabel(
            with: settings, 
            enabledElementsCount: enabledElementsCount,
            customFontSizeFactors: factors
        )
        
        // 设置尺寸
        let maxWidth = container.bounds.width * WatermarkConstants.Custom5.labelMaxWidthContainerWidthFactor
        let size = label.sizeThatFits(CGSize(width: maxWidth, height: .greatestFiniteMagnitude))
        label.frame.size = size
        
        container.addSubview(label)
        self.textLabel = label
    }
    
    /// 创建偏好标签
    private func createPreferenceLabel(_ container: UIView, settings: WatermarkSettings, enabledElementsCount: Int) {
        // 使用PreferenceLabelCreator创建标签
        let factors = (
            single: WatermarkConstants.Custom5.fontSingleElementFactor, 
            two: WatermarkConstants.Custom5.fontTwoElementsFactor, 
            three: WatermarkConstants.Custom5.fontThreeElementsFactor
        )
        
        let label = PreferenceLabelCreator.createLabel(
            for: settings.preferenceOption, 
            with: settings, 
            enabledElementsCount: enabledElementsCount,
            customFontSizeFactors: factors
        )
        
        // 设置尺寸
        let maxWidth = container.bounds.width * WatermarkConstants.Custom5.labelMaxWidthContainerWidthFactor
        let size = label.sizeThatFits(CGSize(width: maxWidth, height: .greatestFiniteMagnitude))
        label.frame.size = size
        
        container.addSubview(label)
        self.preferenceLabel = label
    }
    
    /// 创建署名标签
    private func createSignatureLabel(_ container: UIView, settings: WatermarkSettings, enabledElementsCount: Int) {
        // 使用与文字相同的字体大小因子计算基础大小
        let baseSize: CGFloat
        switch enabledElementsCount {
        case 1:
            baseSize = WatermarkConstants.Custom5.fontSingleElementFactor
        case 2:
            baseSize = WatermarkConstants.Custom5.fontTwoElementsFactor
        default:
            baseSize = WatermarkConstants.Custom5.fontThreeElementsFactor
        }
        var fontSize = UIScreen.main.bounds.height * baseSize
        
        // 应用署名大小乘数
        fontSize *= CGFloat(settings.signatureFontSizeMultiplier)
        
        let label = TextUtils.createSignatureLabel(
            with: settings,
            fontSize: fontSize
        )
        
        // 设置尺寸
        let maxWidth = container.bounds.width * WatermarkConstants.Custom5.labelMaxWidthContainerWidthFactor
        let size = label.sizeThatFits(CGSize(width: maxWidth, height: .greatestFiniteMagnitude))
        label.frame.size = size
        
        container.addSubview(label)
        self.signatureLabel = label
        
        print("✅ 水印5 署名标签: 字体大小=\(fontSize), 元素数量=\(enabledElementsCount), 大小乘数=\(settings.signatureFontSizeMultiplier)")
    }
    
    /// 布局水印元素
    private func arrangeElementsLayout(_ container: UIView, enabledCount: Int) {
        let centerX = container.bounds.width / 2
        let centerY = container.bounds.height / 2
        
        // 根据元素的数量，安排垂直布局
        switch enabledCount {
        case 1:
            // 单个元素：垂直居中
            if let logo = self.logoView {
                logo.center = CGPoint(x: centerX, y: centerY)
            } else if let signature = self.signatureLabel {
                signature.sizeToFit()
                signature.center = CGPoint(x: centerX, y: centerY)
            } else if let text = self.textLabel {
                text.sizeToFit() // 确保标签的大小正确反映其内容
                text.center = CGPoint(x: centerX, y: centerY)
            } else if let preference = self.preferenceLabel {
                preference.sizeToFit()
                preference.center = CGPoint(x: centerX, y: centerY)
            }
            
        case 2:
            // 两个元素：垂直排列，间隔开
            // 判断是否包含了Logo
            let hasLogo = self.logoView != nil
            
            // 根据是否包含Logo选择不同的垂直间距
            let verticalSpacing: CGFloat
            if !hasLogo {
                // 如果两个元素都不是Logo（即署名和文字/偏好的组合），使用较小的间距
                verticalSpacing = UIScreen.main.bounds.height * WatermarkConstants.Custom5.layout2ElementsNoLogoVerticalSpacingScreenHeightFactor
            } else {
                // 如果包含Logo，使用正常间距
                verticalSpacing = UIScreen.main.bounds.height * WatermarkConstants.Custom5.layout2ElementsVerticalSpacingScreenHeightFactor
            }
            
            if let logo = self.logoView, let signature = self.signatureLabel {
                // Logo和署名 - 垂直布局
                // Logo放在上面
                let logoY = centerY - verticalSpacing / 2
                logo.center = CGPoint(x: centerX, y: logoY)
                
                // 署名放在下面
                signature.sizeToFit()
                let signatureY = centerY + verticalSpacing / 2
                signature.center = CGPoint(x: centerX, y: signatureY)
                print("✅ 水印5两元素垂直布局: LOGO在上(y=\(logoY)), 署名在下(y=\(signatureY))")
            } else if let logo = self.logoView, let text = self.textLabel {
                // Logo和文字 - 垂直布局
                // Logo放在上面
                let logoY = centerY - verticalSpacing / 2
                logo.center = CGPoint(x: centerX, y: logoY)
                
                // 文字放在下面
                text.sizeToFit() // 确保标签尺寸正确
                let textY = centerY + verticalSpacing / 2
                text.center = CGPoint(x: centerX, y: textY)
                print("✅ 水印5两元素垂直布局: LOGO在上(y=\(logoY)), 文字在下(y=\(textY))")
            } else if let logo = self.logoView, let preference = self.preferenceLabel {
                // Logo和偏好 - 垂直布局
                // Logo放在上面
                let logoY = centerY - verticalSpacing / 2
                logo.center = CGPoint(x: centerX, y: logoY)
                
                // 偏好放在下面
                preference.sizeToFit()
                let preferenceY = centerY + verticalSpacing / 2
                preference.center = CGPoint(x: centerX, y: preferenceY)
                print("✅ 水印5两元素垂直布局: LOGO在上(y=\(logoY)), 偏好在下(y=\(preferenceY))")
            } else if let signature = self.signatureLabel, let text = self.textLabel {
                // 署名和文字 - 垂直布局
                // 署名放在上面
                signature.sizeToFit()
                let signatureY = centerY - verticalSpacing / 2
                signature.center = CGPoint(x: centerX, y: signatureY)
                
                // 文字放在下面
                text.sizeToFit()
                let textY = centerY + verticalSpacing / 2
                text.center = CGPoint(x: centerX, y: textY)
                print("✅ 水印5两元素垂直布局: 署名在上(y=\(signatureY)), 文字在下(y=\(textY)), 间距:\(verticalSpacing)")
            } else if let signature = self.signatureLabel, let preference = self.preferenceLabel {
                // 署名和偏好 - 垂直布局
                // 署名放在上面
                signature.sizeToFit()
                let signatureY = centerY - verticalSpacing / 2
                signature.center = CGPoint(x: centerX, y: signatureY)
                
                // 偏好放在下面
                preference.sizeToFit()
                let preferenceY = centerY + verticalSpacing / 2
                preference.center = CGPoint(x: centerX, y: preferenceY)
                print("✅ 水印5两元素垂直布局: 署名在上(y=\(signatureY)), 偏好在下(y=\(preferenceY)), 间距:\(verticalSpacing)")
            } else if let text = self.textLabel, let preference = self.preferenceLabel {
                // 文字和偏好 - 垂直布局
                // 文字放在上面
                text.sizeToFit()
                let textY = centerY - verticalSpacing / 2
                text.center = CGPoint(x: centerX, y: textY)
                
                // 偏好放在下面
                preference.sizeToFit()
                let preferenceY = centerY + verticalSpacing / 2
                preference.center = CGPoint(x: centerX, y: preferenceY)
                print("✅ 水印5两元素垂直布局: 文字在上(y=\(textY)), 偏好在下(y=\(preferenceY))")
            }
            
        case 3:
            // 三个元素：垂直排列，等间距
            let totalHeight = container.bounds.height
            let verticalSpacing = UIScreen.main.bounds.height * WatermarkConstants.Custom5.layout3ElementsVerticalSpacingScreenHeightFactor // 使用基于屏幕高度的常量
            
            if let logo = self.logoView, let signature = self.signatureLabel, let text = self.textLabel {
                // Logo放在上方
                let logoY = centerY - verticalSpacing
                logo.center = CGPoint(x: centerX, y: logoY)
                
                // 署名放在中间
                signature.sizeToFit()
                signature.center = CGPoint(x: centerX, y: centerY)
                
                // 文字放在下方
                text.sizeToFit()
                let textY = centerY + verticalSpacing
                text.center = CGPoint(x: centerX, y: textY)
                
                print("✅ 水印5三元素垂直布局: Logo在上(y=\(logoY)), 署名在中(y=\(centerY)), 文字在下(y=\(textY))")
            } else if let logo = self.logoView, let signature = self.signatureLabel, let preference = self.preferenceLabel {
                // Logo放在上方
                let logoY = centerY - verticalSpacing
                logo.center = CGPoint(x: centerX, y: logoY)
                
                // 署名放在中间
                signature.sizeToFit()
                signature.center = CGPoint(x: centerX, y: centerY)
                
                // 偏好放在下方
                preference.sizeToFit()
                let preferenceY = centerY + verticalSpacing
                preference.center = CGPoint(x: centerX, y: preferenceY)
                
                print("✅ 水印5三元素垂直布局: Logo在上(y=\(logoY)), 署名在中(y=\(centerY)), 偏好在下(y=\(preferenceY))")
            } else if let logo = self.logoView, let text = self.textLabel, let preference = self.preferenceLabel {
                // Logo放在上方
                let logoY = centerY - verticalSpacing
                logo.center = CGPoint(x: centerX, y: logoY)
                
                // 文字放在中间
                text.sizeToFit()
                text.center = CGPoint(x: centerX, y: centerY)
                
                // 偏好放在下方
                preference.sizeToFit()
                let preferenceY = centerY + verticalSpacing
                preference.center = CGPoint(x: centerX, y: preferenceY)
                
                print("✅ 水印5三元素垂直布局: Logo在上(y=\(logoY)), 文字在中(y=\(centerY)), 偏好在下(y=\(preferenceY))")
            }
            
        default:
            // 其他情况：处理降级情况
            break
        }
    }
    
    /// 移除自定义水印5效果
    func remove(from previewContainer: UIView) {
        guard let wrapper = self.scaledFrameWrapper,
              let contentView = self.originalContentView,
              let superview = self.originalSuperview,
              let originalFrame = self.originalFrameInSuperview,
              let originalTransform = self.originalTransform,
              let originalIndex = self.originalIndexInSuperview
        else {
            // 如果 wrapper 仍然存在于 previewContainer 中，则尝试移除它
            self.scaledFrameWrapper?.removeFromSuperview()
            cleanupStoredState()
            return
        }

        // 恢复原始状态
        contentView.removeFromSuperview()
        
        contentView.transform = .identity
        contentView.frame = originalFrame
        contentView.transform = originalTransform
        
        if superview.subviews.count > originalIndex {
            superview.insertSubview(contentView, at: originalIndex)
        } else {
            superview.addSubview(contentView)
        }
        
        wrapper.removeFromSuperview()
        cleanupStoredState()
        print("✅ CustomWatermarkStyle5: 已移除自定义水印5。")
    }
    
    /// 恢复原始视图状态
    private func restoreOriginalContentViewState() {
        guard let contentView = self.originalContentView,
              let superview = self.originalSuperview,
              let originalFrame = self.originalFrameInSuperview,
              let originalTransform = self.originalTransform,
              let originalIndex = self.originalIndexInSuperview else {
            return
        }
        contentView.removeFromSuperview()
        contentView.transform = .identity
        contentView.frame = originalFrame
        contentView.transform = originalTransform
        if superview.subviews.count > originalIndex {
            superview.insertSubview(contentView, at: originalIndex)
        } else {
            superview.addSubview(contentView)
        }
        cleanupStoredState()
    }

    /// 清理存储的状态
    private func cleanupStoredState() {
        self.originalContentView = nil
        self.originalFrameInSuperview = nil
        self.originalTransform = nil
        self.originalSuperview = nil
        self.originalIndexInSuperview = nil
        self.scaledFrameWrapper = nil
        self.logoView = nil
        self.textLabel = nil
        self.preferenceLabel = nil
        self.preferenceLabel = nil
        self.itemsContainer = nil
        self.watermarkSettings = nil
        self.signatureLabel = nil
    }
} 