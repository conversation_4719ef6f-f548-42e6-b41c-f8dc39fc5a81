import UIKit

// MARK: - 自定义水印1
/// 自定义水印1 - 在预览图像周围添加简单的边框水印
class CustomWatermarkStyle1: WatermarkStyle {
    /// 左右边框宽度（在整体缩放前定义）
    private let sideBorderWidth: CGFloat
    
    /// 上边框宽度
    private let topBorderWidth: CGFloat
    
    /// 下边框宽度
    private let bottomBorderWidth: CGFloat
    
    /// 边框颜色
    private let borderColor: UIColor
    
    // --- 存储原始状态和中间视图 ---
    private weak var originalContentView: UIView?
    private var originalFrameInSuperview: CGRect? // contentView在其原始父视图中的frame
    private var originalTransform: CGAffineTransform?
    private weak var originalSuperview: UIView?
    private var originalIndexInSuperview: Int?

    /// 这个视图包含了 contentView 和它周围的边框，它会被缩放并居中到 previewContainer
    private weak var scaledBorderedContentViewWrapper: UIView?

    /// 用于标记处理的是相机预览还是照片模式
    private var isProcessingCameraPreview: Bool = false
    
    // --- 水印元素相关 ---
    private weak var bottomItemsContainer: UIView? // 底部内容元素容器
    private weak var logoView: UIImageView? // Logo图片视图
    private weak var signatureLabel: UILabel? // 署名标签
    private weak var textLabel: UILabel? // 水印文字标签
    private weak var preferenceLabel: UILabel? // 偏好选项标签
    private var watermarkSettings: WatermarkSettings? // 保存水印设置的引用

    /// 初始化
    /// - Parameters:
    ///   - borderWidth: 基本边框宽度（用于左右边框）
    ///   - topBottomBorderWidth: 上下边框宽度，默认与borderWidth相同
    ///   - borderColor: 边框颜色，默认为白色
    init(borderWidth: CGFloat, topBottomBorderWidth: CGFloat? = nil, borderColor: UIColor = .white) {
        self.sideBorderWidth = borderWidth
        self.topBorderWidth = topBottomBorderWidth ?? borderWidth
        self.bottomBorderWidth = topBottomBorderWidth ?? borderWidth
        self.borderColor = borderColor
    }
    
    /// 查找实际的内容视图
    private func findActualContentView(in container: UIView) -> UIView? {
        print("🔍 [CustomWatermarkStyle1] 查找内容视图 - 容器类型: \(type(of: container)), 尺寸: \(container.bounds)")
        print("   - 容器子视图数量: \(container.subviews.count)")
        for (index, subview) in container.subviews.enumerated() {
            print("   - 子视图[\(index)]: \(type(of: subview)), tag: \(subview.tag), 尺寸: \(subview.bounds)")
        }

        var contentView: UIView? = nil

        // 优先查找我们为导入图片创建的 imageHostView (tag 123 from MockPreviewView.imageHostViewTag)
        if let imageHost = container.viewWithTag(123) {
            contentView = imageHost
            isProcessingCameraPreview = false
            print("✅ [CustomWatermarkStyle1] 发现照片模式的 imageHostView (tag 123)")
            print("   - imageHostView尺寸: \(imageHost.bounds)")
            print("   - imageHostView子视图数量: \(imageHost.subviews.count)")
            return contentView
        }

        // 查找相机预览视图
        var cameraPreviewView: UIView? = nil
        
        // 查找 MockCameraPreviewService 创建的中间容器 (tag 100) 和预览视图 (tag 101)
        if let intermediateContainer = container.viewWithTag(100) {
            if let cameraPreview = intermediateContainer.viewWithTag(101) {
                cameraPreviewView = cameraPreview
                print("CustomWatermarkStyle1: 发现相机预览视图 (tag 101 in tag 100)")
            }
        }
        // 容器本身是相机预览视图 (tag 101)
        else if container.tag == 101 { 
            cameraPreviewView = container
            print("CustomWatermarkStyle1: 容器本身是相机预览视图 (tag 101)")
        }
        
        // 如果找到了相机预览视图，使用它或创建其副本
        if let cameraPreview = cameraPreviewView {
            isProcessingCameraPreview = true
            print("✅ [CustomWatermarkStyle1] 发现相机预览视图")
            print("   - 相机预览尺寸: \(cameraPreview.bounds)")

            // 如果发现这是相机预览视图，我们需要特殊处理
            // 由于相机预览可能已经被预先缩放，我们需要确保这里的处理与照片模式一致
            return cameraPreview
        }
        
        // 降级：查找第一个 UIImageView
        if let imageView = container.subviews.first(where: { $0 is UIImageView }) {
            print("⚠️ [CustomWatermarkStyle1] 使用第一个 UIImageView 作为内容视图")
            print("   - UIImageView尺寸: \(imageView.bounds)")
            if let imgView = imageView as? UIImageView {
                print("   - UIImageView.image: \(imgView.image != nil ? "存在(\(imgView.image!.size))" : "nil")")
            }
            return imageView
        }

        print("⚠️ [CustomWatermarkStyle1] 未找到特定的内容视图，尝试使用第一个子视图")
        // 最后的降级：尝试取第一个子视图
        if let firstSubview = container.subviews.first {
            print("   - 第一个子视图类型: \(type(of: firstSubview)), 尺寸: \(firstSubview.bounds)")
            return firstSubview
        }

        print("❌ [CustomWatermarkStyle1] 无法确定内容视图，且容器内无子视图")
        return nil // 如果没有任何可识别的内容视图
    }
    
    /// 应用白色边框水印
    func apply(to previewContainer: UIView) {
        print("🎨 [CustomWatermarkStyle1] 开始应用水印")
        print("   - 预览容器类型: \(type(of: previewContainer)), 尺寸: \(previewContainer.bounds)")

        if scaledBorderedContentViewWrapper != nil {
            print("🔄 [CustomWatermarkStyle1] 移除旧的水印")
            remove(from: previewContainer) // 先移除旧的
        }

        guard let contentView = findActualContentView(in: previewContainer) else {
            print("❌ [CustomWatermarkStyle1] apply - 无法找到实际内容视图")
            return
        }

        print("✅ [CustomWatermarkStyle1] 找到内容视图: \(type(of: contentView)), 尺寸: \(contentView.bounds)")

        // 获取水印设置以检查阴影效果和模糊边框效果是否启用
        let settings = WatermarkSettingsManager.shared.getSettings()
        self.watermarkSettings = settings
        let isShadowEnabled = settings.isShadowEnabled
        // 获取模糊边框设置
        let isBlurBorderEnabled = settings.isBlurBorderEnabled
        let blurIntensity = settings.blurIntensity

        // 1. 保存原始状态
        self.originalContentView = contentView
        self.originalTransform = contentView.transform // 保存原始变换
        self.originalSuperview = contentView.superview
        if let superview = contentView.superview {
            self.originalFrameInSuperview = contentView.frame // Frame相对于其父视图
            self.originalIndexInSuperview = superview.subviews.firstIndex(of: contentView)
        } else {
            self.originalFrameInSuperview = contentView.bounds
            self.originalIndexInSuperview = 0 // 假设是第一个或唯一一个
        }

        // --- 步骤1: 创建一个包含 contentView 和边框的视图 (`borderedView`) ---
        let contentOriginalSize = contentView.bounds.size // 使用bounds获取无变换的尺寸
        
        // 为相机预览模式添加特殊处理
        if isProcessingCameraPreview {
            print("CustomWatermarkStyle1: 正在处理相机预览模式，确保内容尺寸正确")
            // 在相机预览模式下，内容视图可能已经被缩放，这里确保使用合适的尺寸
        }
        
        // `borderedView` 的尺寸 = 内容尺寸 + 左右边框宽度 + 上下边框宽度
        let borderedViewWidth = contentOriginalSize.width + (2 * self.sideBorderWidth)
        let borderedViewHeight = contentOriginalSize.height + self.topBorderWidth + self.bottomBorderWidth
        let borderedViewSize = CGSize(width: borderedViewWidth, height: borderedViewHeight)

        let borderedView = UIView(frame: CGRect(origin: .zero, size: borderedViewSize))
        borderedView.clipsToBounds = false // 允许子视图的阴影溢出
        
        // 根据模糊边框设置决定背景 - 两种模式完全互斥
        print("🔍 [CustomWatermarkStyle1] 模糊边框设置: \(isBlurBorderEnabled)")
        if isBlurBorderEnabled {
            // 模糊边框模式 - 背景透明
            borderedView.backgroundColor = .clear
            print("🔍 [CustomWatermarkStyle1] 开始捕获视图用于模糊处理")

            // 使用通用方法捕获内容视图并应用模糊效果
            if let capturedImage = WatermarkStyleUtils.captureView(contentView) {
                print("✅ [CustomWatermarkStyle1] 视图捕获成功，图像尺寸: \(capturedImage.size)")
                // 检测是否为照片模式，如果是则使用延迟应用
                if !isProcessingCameraPreview {
                    print("📸 [CustomWatermarkStyle1] 照片模式，使用延迟模糊应用")
                    WatermarkStyleUtils.applyBlurBackgroundDelayed(to: borderedView, with: capturedImage, intensity: blurIntensity, settings: settings, delay: 0.2)
                } else {
                    print("📹 [CustomWatermarkStyle1] 相机预览模式，立即应用模糊")
                    WatermarkStyleUtils.applyBlurBackground(to: borderedView, with: capturedImage, intensity: blurIntensity, settings: settings)
                }
            } else {
                // 截图失败时回退到默认背景
                print("❌ [CustomWatermarkStyle1] 无法获取视图截图，使用默认背景")
                borderedView.backgroundColor = self.borderColor
            }
        } else {
            // 普通边框模式 - 设置背景颜色
            print("🔍 [CustomWatermarkStyle1] 使用普通边框模式")
            borderedView.backgroundColor = self.borderColor
        }
        
        // 将 contentView 移入 borderedView，并考虑不同的上下左右边框宽度
        contentView.transform = .identity // 在borderedView内部，先重置变换以便设置frame
        contentView.frame = CGRect(x: self.sideBorderWidth, 
                                  y: self.topBorderWidth, 
                                  width: contentOriginalSize.width, 
                                  height: contentOriginalSize.height)
        borderedView.addSubview(contentView) // contentView 现在是 borderedView 的子视图

        // 应用阴影效果（如果启用）- 使用通用扩展方法
        WatermarkStyleUtils.applyShadowEffect(to: contentView, in: borderedView, isShadowEnabled: isShadowEnabled, styleIdentifier: "CustomWatermarkStyle1")
        
        // --- 创建容纳水印元素的容器视图 ---
        let bottomItemsContainerFrame = CGRect(
            x: self.sideBorderWidth, 
            y: self.topBorderWidth + contentOriginalSize.height - UIScreen.main.bounds.height * 0.05, // 内容底部上方留出5%屏幕高度的区域
            width: contentOriginalSize.width,
            height: UIScreen.main.bounds.height * 0.05 // 高度为5%屏幕高度
        )
        
        let bottomContainer = UIView(frame: bottomItemsContainerFrame)
        bottomContainer.backgroundColor = .clear
        borderedView.addSubview(bottomContainer)
        // 设置底部容器的z轴位置高于内容视图，确保在开启阴影效果时水印元素不被内容视图遮挡
        bottomContainer.layer.zPosition = 0.3 // 高于内容视图的0.2
        self.bottomItemsContainer = bottomContainer
        
        // 添加水印元素到底部容器
        addWatermarkElementsToBottomContainer(bottomContainer)

        // --- 步骤2: 将 `borderedView` (作为整体) 缩放并居中到 `previewContainer` ---
        self.scaledBorderedContentViewWrapper = borderedView // 保存对此视图的引用

        // 将 borderedView 添加到 previewContainer
        previewContainer.addSubview(borderedView)

        // 计算缩放比例以使 borderedView 适应 previewContainer
        let containerSize = previewContainer.bounds.size
        guard borderedView.bounds.width > 0, borderedView.bounds.height > 0 else {
            print("❌ CustomWatermarkStyle1: borderedView 尺寸为0，无法计算缩放")
            borderedView.removeFromSuperview() // 清理
            restoreOriginalContentViewState()
            return
        }

        let scaleX = containerSize.width / borderedView.bounds.width
        let scaleY = containerSize.height / borderedView.bounds.height
        let finalScaleFactor = min(scaleX, scaleY)

        // 应用缩放和居中
        borderedView.transform = CGAffineTransform(scaleX: finalScaleFactor, y: finalScaleFactor)
        borderedView.center = CGPoint(x: containerSize.width / 2, y: containerSize.height / 2)
        
        // 输出详细日志，帮助诊断两种模式下的差异
        print("✅ CustomWatermarkStyle1: 已应用边框。模式: \(isProcessingCameraPreview ? "相机预览" : "照片"), 原始内容尺寸: \(contentOriginalSize), 边框宽度: \(self.sideBorderWidth), 最终缩放比例: \(finalScaleFactor)")
    }
    
    /// 移除边框水印效果
    func remove(from previewContainer: UIView) {
        // 清理水印元素
        self.textLabel?.removeFromSuperview()
        self.signatureLabel?.removeFromSuperview()
        self.preferenceLabel?.removeFromSuperview()
        self.logoView?.removeFromSuperview()
        self.bottomItemsContainer?.removeFromSuperview()
        
        guard let borderedView = self.scaledBorderedContentViewWrapper,
              let contentView = self.originalContentView,
              let superview = self.originalSuperview,
              let originalFrame = self.originalFrameInSuperview,
              let originalTransform = self.originalTransform,
              let originalIndex = self.originalIndexInSuperview
        else {
            // print("ℹ️ CustomWatermarkStyle1: remove - 无需移除，未找到相关视图或原始状态。")
            // 如果 borderedView 仍然存在于 previewContainer 中，也尝试移除它
            self.scaledBorderedContentViewWrapper?.removeFromSuperview()
            cleanupStoredState()
            return
        }

        // 1. 将 contentView 从 borderedView 中移回其原始父视图
        contentView.removeFromSuperview() // 从 borderedView 移除
        
        // 2. 恢复 contentView 的原始 frame 和 transform
        // 注意：frame 是相对于其父视图的，transform 是独立的
        contentView.transform = .identity // 先重置，再设置frame，最后应用原始transform
        contentView.frame = originalFrame
        contentView.transform = originalTransform // 恢复原始变换
        
        // 确保 contentView 被添加到正确的父视图和索引
        if superview.subviews.count > originalIndex {
            superview.insertSubview(contentView, at: originalIndex)
        } else {
            superview.addSubview(contentView) // 作为降级
        }
        
        // 3. 从 previewContainer 中移除 borderedView
        borderedView.removeFromSuperview()

        // 4. 清理存储的状态
        cleanupStoredState()
        print("✅ CustomWatermarkStyle1: 已移除边框。")
    }
    
    private func restoreOriginalContentViewState() {
        guard let contentView = self.originalContentView,
              let superview = self.originalSuperview,
              let originalFrame = self.originalFrameInSuperview,
              let originalTransform = self.originalTransform,
              let originalIndex = self.originalIndexInSuperview else {
            return
        }
        contentView.removeFromSuperview() //确保从任何当前父视图中移除
        contentView.transform = .identity
        contentView.frame = originalFrame
        contentView.transform = originalTransform
        if superview.subviews.count > originalIndex {
            superview.insertSubview(contentView, at: originalIndex)
        } else {
            superview.addSubview(contentView)
        }
        cleanupStoredState()
    }

    private func cleanupStoredState() {
        self.originalContentView = nil
        self.originalFrameInSuperview = nil
        self.originalTransform = nil
        self.originalSuperview = nil
        self.originalIndexInSuperview = nil
        self.scaledBorderedContentViewWrapper = nil
        self.textLabel = nil
        self.bottomItemsContainer = nil
        self.watermarkSettings = nil
    }
    
    /// 添加水印元素到底部容器
    private func addWatermarkElementsToBottomContainer(_ container: UIView) {
        guard let settings = self.watermarkSettings else { return }
        
        // 确定需要显示哪些元素
        let showLogo = !settings.selectedLogo.isEmpty // Logo不为空字符串表示启用
        let showText = settings.isWatermarkTextEnabled && !settings.watermarkText.isEmpty // 文字启用且不为空
        // 添加对签名和偏好选项的检测
        let showSignature = settings.isWatermarkSignatureEnabled && !settings.watermarkSignature.isEmpty // 签名启用且不为空
        
        // 检查新格式或旧格式的偏好选项
        let showPreference = !settings.selectedPreferences.isEmpty || settings.preferenceOption != "OFF" // 存在选中的偏好选项或旧格式不为OFF
        
        // 统计启用的元素数量
        let enabledElementsCount = [showLogo, showText, showSignature, showPreference].filter { $0 }.count
        
        // 如果没有任何元素启用，直接返回
        if enabledElementsCount == 0 { return }
        
        let containerHeight = container.bounds.height
        let containerWidth = container.bounds.width
        
        // 获取位置设置
        let position = settings.positionOption // "中" 或 "下"
        let isCenter = position == "中" // 如果是"中"则居中显示，否则在底部显示
        
        // 如果需要显示Logo，创建Logo视图
        if showLogo {
            createLogoView(container, settings: settings, enabledElementsCount: enabledElementsCount)
        }
        
        // 如果需要显示文字，创建文字标签
        if showText {
            createTextLabel(container, settings: settings, enabledElementsCount: enabledElementsCount)
        }
        
        // 如果需要显示签名，创建签名标签
        if showSignature {
            createSignatureLabel(container, settings: settings, enabledElementsCount: enabledElementsCount)
        }
        
        // 如果需要显示偏好选项，创建偏好标签
        if showPreference {
            createPreferenceLabel(container, settings: settings, enabledElementsCount: enabledElementsCount)
        }
        
        // 根据启用的元素数量，安排布局
        arrangeElementsLayout(container, enabledCount: enabledElementsCount, position: position)
    }
    
    /// 创建Logo视图
    private func createLogoView(_ container: UIView, settings: WatermarkSettings, enabledElementsCount: Int) {
        // 使用固定的2.5%屏幕高度作为Logo大小
        let logoSize = UIScreen.main.bounds.height * 0.025
        
        let logoImageView = LogoCreator.createLogo(
            with: settings,
            fixedSize: logoSize
        )
        
        container.addSubview(logoImageView)
        self.logoView = logoImageView
        
        print("✅ 水印1 Logo: 大小=\(logoSize), 元素数量=\(enabledElementsCount)")
    }
    
    /// 创建文字标签
    private func createTextLabel(_ container: UIView, settings: WatermarkSettings, enabledElementsCount: Int) {
        // 使用固定的1%屏幕高度作为字体大小
        let fontSize = UIScreen.main.bounds.height * WatermarkConstants.Border.fontSizeScreenHeightFactor
        
        let label = TextLabelCreator.createLabel(
            with: settings, 
            enabledElementsCount: enabledElementsCount,
            fixedFontSize: fontSize
        )
        
        // 设置尺寸
        let maxWidth = container.bounds.width * WatermarkConstants.Border.labelMaxWidthContainerWidthFactor
        let size = label.sizeThatFits(CGSize(width: maxWidth, height: .greatestFiniteMagnitude))
        label.frame.size = size
        
        container.addSubview(label)
        self.textLabel = label
        
        print("✅ 水印1 文字标签: 字体大小=\(fontSize), 元素数量=\(enabledElementsCount)")
    }
    
    /// 创建签名标签
    private func createSignatureLabel(_ container: UIView, settings: WatermarkSettings, enabledElementsCount: Int) {
        // 使用固定的1%屏幕高度作为字体大小
        let fontSize = UIScreen.main.bounds.height * WatermarkConstants.Border.fontSizeScreenHeightFactor
        // 应用签名大小乘数
        let finalFontSize = fontSize * CGFloat(settings.signatureFontSizeMultiplier)
        
        let label = UILabel()
        label.text = settings.watermarkSignature
        label.textAlignment = .center
        label.numberOfLines = 0 // 允许多行文本
        
        // 应用字体样式，标记为署名类型
        WatermarkStyleUtils.applyFontStyle(to: label, with: settings, fontSize: finalFontSize, styleIdentifier: "CustomWatermarkStyle1.Signature", isSignature: true)
        
        // 设置尺寸
        let maxWidth = container.bounds.width * WatermarkConstants.Border.labelMaxWidthContainerWidthFactor
        let size = label.sizeThatFits(CGSize(width: maxWidth, height: .greatestFiniteMagnitude))
        label.frame.size = size
        
        container.addSubview(label)
        self.signatureLabel = label
        
        print("✅ 水印1 签名标签: 字体大小=\(finalFontSize), 元素数量=\(enabledElementsCount)")
    }
    
    /// 创建偏好标签
    private func createPreferenceLabel(_ container: UIView, settings: WatermarkSettings, enabledElementsCount: Int) {
        // 使用固定的1%屏幕高度作为字体大小
        let fontSize = UIScreen.main.bounds.height * WatermarkConstants.Border.fontSizeScreenHeightFactor
        // 应用偏好缩放因子
        let finalFontSize = fontSize * CGFloat(settings.preferenceScaleFactor)
        
        // 创建偏好标签
        let preferenceLabel = PreferenceLabelCreator.createLabel(
            for: settings.preferenceOption,
            with: settings,
            enabledElementsCount: enabledElementsCount,
            fixedFontSize: finalFontSize
        )
        
        // 设置尺寸
        let maxWidth = container.bounds.width * WatermarkConstants.Border.labelMaxWidthContainerWidthFactor
        let size = preferenceLabel.sizeThatFits(CGSize(width: maxWidth, height: .greatestFiniteMagnitude))
        preferenceLabel.frame.size = size
        
        container.addSubview(preferenceLabel)
        self.preferenceLabel = preferenceLabel
        
        print("✅ 水印1 偏好标签: 字体大小=\(finalFontSize), 元素数量=\(enabledElementsCount)")
    }
    
    /// 根据元素数量和位置安排布局
    private func arrangeElementsLayout(_ container: UIView, enabledCount: Int, position: String) {
        let containerHeight = container.bounds.height
        let containerWidth = container.bounds.width
        let bottomPadding: CGFloat = containerHeight * WatermarkConstants.Border.bottomPaddingContainerHeightFactor
        
            // 参考水印8的垂直间距
            let verticalSpacing = UIScreen.main.bounds.height * WatermarkConstants.Custom8.stackViewSpacingFactor
            
        // 收集所有可见元素
        var visibleElements: [(view: UIView, order: Int)] = []
        
        if let logo = self.logoView {
            visibleElements.append((logo, 1)) // Logo优先级最高，排在最上方
        }
        
        if let signature = self.signatureLabel {
            visibleElements.append((signature, 2)) // 署名优先级第二，排在Logo后面
        }
        
        if let text = self.textLabel {
            visibleElements.append((text, 3)) // 文字优先级第三
        }
        
        if let preference = self.preferenceLabel {
            visibleElements.append((preference, 4)) // 偏好选项优先级最低，排在最下方
        }
        
        // 按优先级排序
        visibleElements.sort { $0.order < $1.order }
        
        // 如果没有可见元素，直接返回
        if visibleElements.isEmpty {
            return
        }
        
        // 计算所有元素高度总和（包括间距）
        var totalHeight: CGFloat = 0
        for (index, element) in visibleElements.enumerated() {
            totalHeight += element.view.bounds.height
            if index < visibleElements.count - 1 {
                totalHeight += verticalSpacing
            }
        }
        
        // 计算起始Y位置
        let startY = containerHeight - bottomPadding - totalHeight
        
        // 依次布局每个元素
        var currentY = startY
        for element in visibleElements {
            let view = element.view
            view.center = CGPoint(x: containerWidth / 2, y: currentY + view.bounds.height / 2)
            
            // 文字类型元素需要设置文本对齐方式
            if let label = view as? UILabel {
                label.textAlignment = .center
            }
            
            currentY += view.bounds.height + verticalSpacing
        }
        
        // 输出日志
        let elementTypes = visibleElements.map { element -> String in
            if element.view === self.logoView {
                return "Logo"
            } else if element.view === self.signatureLabel {
                return "署名"
            } else if element.view === self.textLabel {
                return "文字"
            } else if element.view === self.preferenceLabel {
                return "偏好"
            } else {
                return "未知元素"
            }
        }.joined(separator: "、")
        
        print("✅ 水印1 布局: 从上到下依次为\(elementTypes)的布局，共\(visibleElements.count)个元素")
    }
} 