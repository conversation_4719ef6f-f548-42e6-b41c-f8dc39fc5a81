import SwiftUI
import UIKit

// MARK: - 水印工具类
/// 提供水印样式共享的工具方法
class WatermarkStyleUtils {
    
    /// 将字符串形式的字体粗细转换为UIFont.Weight
    static func getSystemFontWeight(from weightString: String) -> UIFont.Weight {
        switch weightString {
            case "Thin", "ExtraLight": return .thin
            case "Ultralight": return .ultraLight
            case "Light": return .light
            case "Regular", "Normal": return .regular
            case "Medium": return .medium
            case "Semibold", "SemiBold": return .semibold
            case "Bold": return .bold
            case "Heavy", "ExtraBold": return .heavy
            case "Black": return .black
            default: return .regular
        }
    }
    
    /// 计算线性缩放的间距
    /// - Parameters:
    ///   - baseSpacing: 基础间距（滑块在100%位置时的间距）
    ///   - scaleFactor: 缩放因子（0.75-1.25）
    /// - Returns: 线性缩放后的间距
    static func calculateLinearSpacing(baseSpacing: CGFloat, scaleFactor: CGFloat) -> CGFloat {
        // 使用分段线性函数实现准确的倍数关系
        if scaleFactor < 1.0 {
            // 从0.75到1.0的映射，0.75→0.5，1.0→1.0
            let ratio = 0.5 + (scaleFactor - 0.75) * 2.0
            return baseSpacing * ratio
        } else {
            // 从1.0到1.25的映射，1.0→1.0，1.25→1.25
            return baseSpacing * scaleFactor
        }
    }
    
    /// 捕获视图内容生成UIImage - 改进版，支持Metal渲染
    static func captureView(_ view: UIView) -> UIImage? {
        print("🔍 [WatermarkStyles] captureView 开始 - 视图类型: \(type(of: view)), 尺寸: \(view.bounds)")

        // 一系列安全检查
        guard view.superview != nil, view.window != nil else {
            print("❌ [WatermarkStyles] captureView - 视图不在视图层次结构中")
            print("   - superview: \(view.superview != nil ? "存在" : "nil")")
            print("   - window: \(view.window != nil ? "存在" : "nil")")
            return nil
        }

        guard view.bounds.width > 0, view.bounds.height > 0 else {
            print("❌ [WatermarkStyles] captureView - 视图尺寸无效: \(view.bounds)")
            return nil
        }

        // 确保在主线程上执行UI操作
        if !Thread.isMainThread {
            var resultImage: UIImage?
            DispatchQueue.main.sync {
                resultImage = self.captureView(view)
            }
            return resultImage
        }

        // 首先尝试从UIImageView直接获取图像（照片模式优化）
        if let imageView = findImageViewInHierarchy(view) {
            print("🔍 [WatermarkStyles] 找到UIImageView: \(type(of: imageView)), 尺寸: \(imageView.bounds)")
            if let image = imageView.image {
                print("✅ [WatermarkStyles] 直接从UIImageView获取图像 - 图像尺寸: \(image.size)")
                return image
            } else {
                print("⚠️ [WatermarkStyles] UIImageView存在但image为nil")
            }
        } else {
            print("🔍 [WatermarkStyles] 未找到UIImageView，将使用视图捕获")
        }

        // 强制布局更新，确保视图准备就绪
        view.setNeedsLayout()
        view.layoutIfNeeded()

        // 等待一个运行循环，确保Metal渲染完成
        var capturedImage: UIImage?
        let semaphore = DispatchSemaphore(value: 0)

        DispatchQueue.main.async {
            // 使用更安全的图形上下文处理方式
            autoreleasepool {
                UIGraphicsBeginImageContextWithOptions(view.bounds.size, false, UIScreen.main.scale)

                defer {
                    UIGraphicsEndImageContext()
                    semaphore.signal()
                }

                guard let context = UIGraphicsGetCurrentContext() else {
                    print("❌ WatermarkStyle: 无法创建图形上下文")
                    return
                }

                do {
                    // 尝试渲染视图到上下文
                    view.layer.render(in: context)

                    // 从当前上下文获取图像
                    if let image = UIGraphicsGetImageFromCurrentImageContext(),
                       image.size.width > 0,
                       image.size.height > 0 {
                        capturedImage = image
                        print("✅ WatermarkStyle: 成功捕获视图内容")
                    } else {
                        print("⚠️ WatermarkStyle: 捕获的图像无效")
                    }
                } catch {
                    print("❌ WatermarkStyle: 渲染视图到上下文时出错: \(error)")
                }
            }
        }

        // 等待捕获完成，最多等待1秒
        _ = semaphore.wait(timeout: .now() + 1.0)

        return capturedImage
    }

    /// 在视图层次结构中查找UIImageView
    private static func findImageViewInHierarchy(_ view: UIView) -> UIImageView? {
        // 如果当前视图就是UIImageView，直接返回
        if let imageView = view as? UIImageView {
            return imageView
        }

        // 递归查找子视图中的UIImageView
        for subview in view.subviews {
            if let imageView = findImageViewInHierarchy(subview) {
                return imageView
            }
        }

        return nil
    }

    /// 延迟应用模糊背景 - 专门用于照片模式
    static func applyBlurBackgroundDelayed(to view: UIView, with image: UIImage, intensity: Double, settings: WatermarkSettings?, delay: TimeInterval = 0.1) {
        print("🎨 [WatermarkStyles] 延迟应用模糊背景，延迟: \(delay)秒")

        DispatchQueue.main.asyncAfter(deadline: .now() + delay) {
            applyBlurBackground(to: view, with: image, intensity: intensity, settings: settings)
        }
    }
    
    /// 应用模糊背景 - Metal优化版
    static func applyBlurBackground(to view: UIView, with image: UIImage, intensity: Double, settings: WatermarkSettings?) {
        print("🎨 [WatermarkStyles] 开始应用Metal模糊背景")
        print("   - 输入图像尺寸: \(image.size)")
        print("   - 目标视图尺寸: \(view.bounds)")
        print("   - 模糊强度: \(intensity)")

        // 获取模糊样式
        let blurStyle = settings?.blurStyle ?? "systemMaterial"

        // 根据样式选择不同的模糊半径值
        var blurRadius: CGFloat = 10.0 // 默认模糊半径

        switch blurStyle {
        case "systemUltraThinMaterial": // 浅色
            blurRadius = 2.0
        case "systemThinMaterial": // 较浅
            blurRadius = 4.0
        case "systemMaterial": // 标准
            blurRadius = 6.0
        case "systemThickMaterial": // 较深
            blurRadius = 8.0
        case "systemChromeMaterial": // 深色
            blurRadius = 10.0
        default:
            blurRadius = 6.0 // 默认改为标准模糊半径
        }

        print("🎨 [WatermarkStyles] 模糊样式: \(blurStyle), 半径: \(blurRadius)")

        // 步骤1: 放大原始图像1.025倍
        let scale = 1.025
        let scaledWidth = image.size.width * CGFloat(scale)
        let scaledHeight = image.size.height * CGFloat(scale)
        let scaledSize = CGSize(width: scaledWidth, height: scaledHeight)
        
        // 创建放大的图像
        print("🔍 [WatermarkStyles] 创建放大图像 - 原始: \(image.size) -> 放大: \(scaledSize)")
        UIGraphicsBeginImageContextWithOptions(scaledSize, false, image.scale)
        image.draw(in: CGRect(origin: .zero, size: scaledSize))
        let scaledImage = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()

        guard let enlargedImage = scaledImage else {
            print("❌ [WatermarkStyles] 无法创建放大图像，使用原始图像")
            let imageView = UIImageView(image: image)
            imageView.contentMode = .scaleAspectFill
            imageView.frame = view.bounds
            view.addSubview(imageView)
            return
        }
        print("✅ [WatermarkStyles] 放大图像创建成功: \(enlargedImage.size)")
        
        // 使用Metal高斯模糊处理放大后的图像 - 优化版
        print("🔍 [WatermarkStyles] 开始Metal高斯模糊处理")
        let blurredImage: UIImage
        do {
            // 创建Metal引擎时添加重试机制
            var metalEngine: MetalSpecialEffectsEngine?
            var retryCount = 0
            let maxRetries = 3

            print("🔍 [WatermarkStyles] 尝试创建Metal引擎...")
            while metalEngine == nil && retryCount < maxRetries {
                do {
                    metalEngine = try MetalSpecialEffectsEngine()
                    print("✅ [WatermarkStyles] Metal引擎创建成功")
                    break
                } catch {
                    retryCount += 1
                    print("❌ [WatermarkStyles] Metal引擎创建失败 (尝试 \(retryCount)/\(maxRetries)): \(error)")
                    if retryCount < maxRetries {
                        // 短暂等待后重试
                        Thread.sleep(forTimeInterval: 0.1)
                    }
                }
            }

            guard let engine = metalEngine else {
                print("❌ [WatermarkStyles] 所有重试都失败，无法创建Metal引擎")
                throw NSError(domain: "MetalEngine", code: -1, userInfo: [NSLocalizedDescriptionKey: "无法创建Metal引擎"])
            }

            print("🔍 [WatermarkStyles] 开始执行高斯模糊 - 半径: \(blurRadius)")
            blurredImage = try engine.applyGaussianBlur(to: enlargedImage, radius: Float(blurRadius))
            print("✅ [WatermarkStyles] Metal高斯模糊成功，半径: \(blurRadius)")
        } catch {
            print("❌ [WatermarkStyles] Metal高斯模糊失败，回退到普通背景: \(error)")
            print("   - 错误详情: \(error.localizedDescription)")
            let imageView = UIImageView(image: image)
            imageView.contentMode = .scaleAspectFill
            imageView.frame = view.bounds
            view.addSubview(imageView)
            return
        }
        
        // 步骤3: 从模糊后的放大图像中央裁剪出需要的尺寸
        let blurredLargeImage = blurredImage
        
        // 计算需要的区域（从中心裁剪）
        let targetWidth = view.bounds.width
        let targetHeight = view.bounds.height
        
        // 计算裁剪区域在放大图像中的位置（居中）
        let offsetX = (blurredLargeImage.size.width - targetWidth) / 2
        let offsetY = (blurredLargeImage.size.height - targetHeight) / 2
        
        // 创建裁剪区域
        let cropRect = CGRect(x: offsetX, y: offsetY, width: targetWidth, height: targetHeight)
        
        // 裁剪图像
        UIGraphicsBeginImageContextWithOptions(CGSize(width: targetWidth, height: targetHeight), false, blurredLargeImage.scale)
        blurredLargeImage.draw(at: CGPoint(x: -offsetX, y: -offsetY))
        let croppedBlurredImage = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        
        guard let finalBlurredImage = croppedBlurredImage else {
            print("⚠️ 无法裁剪模糊图像，使用未裁剪版本")
            let blurredImageView = UIImageView(image: blurredLargeImage)
            blurredImageView.contentMode = .scaleAspectFill
            blurredImageView.frame = view.bounds
            view.addSubview(blurredImageView)
            return
        }
        
        // 创建并配置模糊图像视图
        let blurredImageView = UIImageView(image: finalBlurredImage)
        blurredImageView.contentMode = .scaleAspectFill
        blurredImageView.frame = view.bounds
        
        // 添加到视图
        view.addSubview(blurredImageView)
        
        print("✅ 应用了放大并裁剪的模糊背景方案，放大比例：1.025倍，模糊样式：\(blurStyle)，半径：\(blurRadius)")
    }
    
    /// 应用阴影效果
    /// - Parameters:
    ///   - contentView: 需要应用阴影的内容视图
    ///   - wrapperView: 内容视图的容器视图，用于确保层级关系
    ///   - isShadowEnabled: 是否启用阴影效果
    ///   - styleIdentifier: 水印样式标识符，用于日志
    static func applyShadowEffect(to contentView: UIView, in wrapperView: UIView, isShadowEnabled: Bool, styleIdentifier: String) {
        if isShadowEnabled {
            print("✅ \(styleIdentifier): 应用阴影效果：isShadowEnabled=\(isShadowEnabled)")
            contentView.layer.shadowColor = UIColor.black.cgColor
            contentView.layer.shadowOffset = CGSize(width: 0, height: 0) // 阴影不偏向任何方向，均匀分布
            contentView.layer.shadowOpacity = 0.4 // 中等透明度为0.4
            contentView.layer.shadowRadius = 5 // 阴影半径为5
            contentView.clipsToBounds = false // 关闭裁剪以显示阴影
            
            // 确保层级关系正确
            wrapperView.bringSubviewToFront(contentView)
            
            // 添加轻微的3D变换效果增强悬浮感
            contentView.layer.zPosition = 0.2
        } else {
            // 确保当阴影禁用时，清除任何可能存在的阴影效果
            print("❌ \(styleIdentifier): 未应用阴影效果")
            contentView.layer.shadowOpacity = 0
            contentView.layer.zPosition = 0
        }
    }
    
    /// 应用字体样式和颜色
    /// - Parameters:
    ///   - label: 需要设置样式的UILabel
    ///   - settings: 水印设置
    ///   - fontSize: 字体大小（由调用者决定）
    ///   - styleIdentifier: 水印样式标识符，用于日志
    ///   - isSignature: 是否为署名标签，用于选择字体粗细设置
    static func applyFontStyle(to label: UILabel, with settings: WatermarkSettings, fontSize: CGFloat, styleIdentifier: String = "WatermarkStyle", isSignature: Bool = false) {
        // 获取字体颜色
        let fontColor = UIColor(
            red: CGFloat(settings.fontColorRed),
            green: CGFloat(settings.fontColorGreen),
            blue: CGFloat(settings.fontColorBlue),
            alpha: CGFloat(settings.fontColorAlpha)
        )
        
        // 判断字体颜色是否为黑色
        let isBlackFont = settings.fontColorRed == 0 && settings.fontColorGreen == 0 && settings.fontColorBlue == 0
        
        // 默认使用字体颜色
        label.textColor = fontColor
        
        // 根据是否为署名选择对应的字体粗细设置
        let fontThickness = isSignature ? settings.signatureFontThicknessMultiplier : settings.fontThicknessMultiplier
        let selectedFontName = settings.selectedFontName
        
        // 获取保存的字体粗细名称
        let selectedFontWeight = isSignature ? settings.selectedSignatureFontWeight : settings.selectedFontWeight

        // Helper function to get the specific font variant name
        func getFontVariantName(fontFamily: String, fontWeight: String) -> String? {
            // 使用保存的字体粗细名称
            if !fontWeight.isEmpty {
                switch fontFamily {
                case "HarmonyOS_Sans_SC":
                    return "HarmonyOS_Sans_SC_" + fontWeight
                case "PingFang-SC":
                    return "PingFangSC-" + fontWeight
                case "SourceHanSansSC":
                    return "SourceHanSansSC-" + fontWeight
                case "HONORSansCN":
                    // 映射SemiBold到DemiBold，Black到Heavy，其他保持不变
                    if fontWeight == "SemiBold" {
                        return "HONORSansCN-DemiBold"
                    } else if fontWeight == "Black" {
                        return "HONORSansCN-Heavy"
                    } else {
                        return "HONORSansCN-" + fontWeight
                    }
                default:
                    return nil
                }
            }
            
            // 如果没有字体粗细名称，返回默认值
            switch fontFamily {
            case "HarmonyOS_Sans_SC":
                return "HarmonyOS_Sans_SC_Regular"
            case "PingFang-SC":
                return "PingFangSC-Regular"
            case "SourceHanSansSC":
                return "SourceHanSansSC-Regular"
            case "HONORSansCN":
                return "HONORSansCN-Regular"
            default:
                return nil
            }
        }

        var appliedCustomFont = false
        if let targetFontName = getFontVariantName(fontFamily: selectedFontName, fontWeight: selectedFontWeight) {
            if let customFont = UIFont(name: targetFontName, size: fontSize) {
                label.font = customFont
                
                // 不管使用什么中文字体，都尝试单独应用英文字体设置
                // 这样即使选择非PingFang字体，英文字体设置也能生效
                WatermarkStyleUtils.configureTextForMixedLanguage(label: label, fontSize: fontSize, fontThickness: fontThickness)
                
                print("✅ \(styleIdentifier): Applied font '\(targetFontName)' at size \(fontSize). Type: \(isSignature ? "Signature" : "Text")")
                appliedCustomFont = true
            } else {
                print("⚠️ \(styleIdentifier): FAILED to load font '\(targetFontName)' for family '\(selectedFontName)' at size \(fontSize). Type: \(isSignature ? "Signature" : "Text")")
            }
        }

        if !appliedCustomFont {
            // Fallback for "系统" or other unhandled/failed custom fonts
            var fontWeight: UIFont.Weight = .regular
            
            // 使用保存的字体粗细名称来确定系统字体的权重，而不是使用数值
            if !selectedFontWeight.isEmpty {
                switch selectedFontWeight {
                case "UltraLight", "Ultralight": fontWeight = .ultraLight
                case "Thin": fontWeight = .thin
                case "Light": fontWeight = .light
                case "Regular": fontWeight = .regular
                case "Medium": fontWeight = .medium
                case "Semibold", "SemiBold": fontWeight = .semibold
                case "Bold": fontWeight = .bold
                case "Heavy", "Black": fontWeight = .heavy
                default: fontWeight = .regular
                }
            } else {
                // 如果没有保存的字体粗细名称，使用默认的regular
                fontWeight = .regular
            }
            
            var actualFontNameForSystemFallback: String? = nil
            switch selectedFontName {
            case "黑体":
                actualFontNameForSystemFallback = WatermarkConstants.Common.defaultPingFangSCSemiboldFont
            case "苹方":
                actualFontNameForSystemFallback = WatermarkConstants.Common.defaultPingFangSCRegularFont
            case "Times":
                actualFontNameForSystemFallback = WatermarkConstants.Common.defaultTimesNewRomanFont
            case "Courier":
                actualFontNameForSystemFallback = WatermarkConstants.Common.defaultCourierNewFont
            default:
                // For "系统" or custom fonts that failed, actualFontNameForSystemFallback remains nil
                // and we use systemFont(ofSize:weight:) directly.
                break
            }

            if let concreteFontName = actualFontNameForSystemFallback, let specificFallbackFont = UIFont(name: concreteFontName, size: fontSize) {
                 label.font = specificFallbackFont
                 print("✅ \(styleIdentifier): Applied specific fallback font '\(concreteFontName)' for '\(selectedFontName)' at size \(fontSize). Type: \(isSignature ? "Signature" : "Text")")
                 
                 // 即使使用了系统字体回退，也要应用英文字体设置
                 WatermarkStyleUtils.configureTextForMixedLanguage(label: label, fontSize: fontSize, fontThickness: fontThickness)
            } else {
                label.font = UIFont.systemFont(ofSize: fontSize, weight: fontWeight)
                print("✅ \(styleIdentifier): Applied SYSTEM font for '\(selectedFontName)'. Weight: \(fontWeight), Size: \(fontSize). Type: \(isSignature ? "Signature" : "Text")")
                
                // 即使使用了默认系统字体，也要应用英文字体设置
                WatermarkStyleUtils.configureTextForMixedLanguage(label: label, fontSize: fontSize, fontThickness: fontThickness)
            }
        }
    }
    
    /// 计算阴影偏移 (CGSize)
    static func calculateShadowOffset(baseOffset: CGFloat = 4.0) -> CGSize {
        return CGSize(width: baseOffset, height: baseOffset)
    }
    
    // 新增方法：配置混合语言字体（为英文和数字使用选定的英文字体）
    static func configureTextForMixedLanguage(label: UILabel, fontSize: CGFloat, fontThickness: CGFloat) {
        // 注意: fontThickness参数已不再使用，但为了保持接口兼容性而保留
        // 确保label有文本
        guard let text = label.text, !text.isEmpty else { return }
        
        // 创建富文本
        let attributedString = NSMutableAttributedString(string: text)
        
        // 获取当前字体作为中文字体 - 保存原有的label.font用于中文字符
        let chineseFont = label.font
        
        // 获取当前水印设置
        let settings = WatermarkSettingsManager.shared.getSettings()
        let selectedEnglishFontName = settings.selectedEnglishFontName
        let selectedEnglishFontWeight = settings.selectedEnglishFontWeight
        
        // 为英文和数字创建字体
        var englishFont: UIFont
        
        // 首先尝试使用用户选择的英文字体
        if selectedEnglishFontName == "FuturaPT" {
            // 直接使用选中的字体粗细名称
            if !selectedEnglishFontWeight.isEmpty {
                let futuraPTFontName = "FuturaPT-" + mapFuturaPTWeight(selectedEnglishFontWeight)
                
                // 尝试创建Futura PT字体
                if let font = UIFont(name: futuraPTFontName, size: fontSize) {
                    englishFont = font
                    print("✅ 应用FuturaPT英文字体: \(futuraPTFontName), 大小: \(fontSize)")
                } else {
                    // 如果无法创建Futura PT字体，回退到使用SF字体
                    englishFont = UIFont.systemFont(ofSize: fontSize, weight: .regular)
                    print("⚠️ 无法创建Futura PT字体，回退到SF字体")
                }
            } else {
                // 如果没有选择字体粗细，使用默认Book
                let futuraPTFontName = "FuturaPT-Book"
                if let font = UIFont(name: futuraPTFontName, size: fontSize) {
                    englishFont = font
                    print("✅ 应用默认FuturaPT-Book英文字体, 大小: \(fontSize)")
                } else {
                    englishFont = UIFont.systemFont(ofSize: fontSize, weight: .regular)
                    print("⚠️ 无法创建默认Futura PT字体，回退到SF字体")
                }
            }
        } else if selectedEnglishFontName == "TYPOGRAPH PRO" {
            // 直接使用选中的字体粗细名称
            if !selectedEnglishFontWeight.isEmpty {
                let typographProFontName = "TYPOGRAPHPRO-" + selectedEnglishFontWeight.replacingOccurrences(of: " ", with: "")
                
                // 尝试创建TYPOGRAPH PRO字体
                if let font = UIFont(name: typographProFontName, size: fontSize) {
                    englishFont = font
                    print("✅ 应用TYPOGRAPH PRO英文字体: \(typographProFontName), 大小: \(fontSize)")
                } else {
                    // 如果无法创建TYPOGRAPH PRO字体，回退到使用SF字体
                    englishFont = UIFont.systemFont(ofSize: fontSize, weight: .regular)
                    print("⚠️ 无法创建TYPOGRAPH PRO字体，回退到SF字体: \(typographProFontName)")
                }
            } else {
                // 如果没有选择字体粗细，使用默认Light
                let typographProFontName = "TYPOGRAPHPRO-Light"
                if let font = UIFont(name: typographProFontName, size: fontSize) {
                    englishFont = font
                    print("✅ 应用默认TYPOGRAPH PRO Light英文字体, 大小: \(fontSize)")
                } else {
                    englishFont = UIFont.systemFont(ofSize: fontSize, weight: .regular)
                    print("⚠️ 无法创建默认TYPOGRAPH PRO字体，回退到SF字体: \(typographProFontName)")
                }
            }
        } else if selectedEnglishFontName == "CourierPrimeCode" {
            // 直接使用选中的字体粗细名称
            if !selectedEnglishFontWeight.isEmpty {
                let courierPrimeCodeFontName = "CourierPrimeCode-" + selectedEnglishFontWeight
                
                // 尝试创建CourierPrimeCode字体
                if let font = UIFont(name: courierPrimeCodeFontName, size: fontSize) {
                    englishFont = font
                    print("✅ 应用CourierPrimeCode英文字体: \(courierPrimeCodeFontName), 大小: \(fontSize)")
                } else {
                    // 如果无法创建CourierPrimeCode字体，回退到使用SF字体
                    englishFont = UIFont.systemFont(ofSize: fontSize, weight: .regular)
                    print("⚠️ 无法创建CourierPrimeCode字体，回退到SF字体")
                }
            } else {
                // 如果没有选择字体粗细，使用默认Regular
                let courierPrimeCodeFontName = "CourierPrimeCode-Regular"
                if let font = UIFont(name: courierPrimeCodeFontName, size: fontSize) {
                    englishFont = font
                    print("✅ 应用默认CourierPrimeCode-Regular英文字体, 大小: \(fontSize)")
                } else {
                    englishFont = UIFont.systemFont(ofSize: fontSize, weight: .regular)
                    print("⚠️ 无法创建默认CourierPrimeCode字体，回退到SF字体")
                }
            }
        } else if selectedEnglishFontName == "Miamo" {
            // 直接使用选中的字体粗细名称
            if !selectedEnglishFontWeight.isEmpty {
                let miamoFontName = "Miamo " + selectedEnglishFontWeight
                
                // 尝试创建Miamo字体
                if let font = UIFont(name: miamoFontName, size: fontSize) {
                    englishFont = font
                    print("✅ 应用Miamo英文字体: \(miamoFontName), 大小: \(fontSize)")
                } else {
                    // 如果无法创建Miamo字体，回退到使用SF字体
                    englishFont = UIFont.systemFont(ofSize: fontSize, weight: .regular)
                    print("⚠️ 无法创建Miamo字体，回退到SF字体")
                }
            } else {
                // 如果没有选择字体粗细，使用默认Regular
                let miamoFontName = "Miamo Regular"
                if let font = UIFont(name: miamoFontName, size: fontSize) {
                    englishFont = font
                    print("✅ 应用默认Miamo Regular英文字体, 大小: \(fontSize)")
                } else {
                    englishFont = UIFont.systemFont(ofSize: fontSize, weight: .regular)
                    print("⚠️ 无法创建默认Miamo字体，回退到SF字体")
                }
            }
        } else if selectedEnglishFontName == "ADELE" {
            // ADELE字体目前只有Light变体
            let adeleFontName = "ADELE-Light"
            if let font = UIFont(name: adeleFontName, size: fontSize) {
                englishFont = font
                print("✅ 应用ADELE英文字体: \(adeleFontName), 大小: \(fontSize)")
            } else {
                englishFont = UIFont.systemFont(ofSize: fontSize, weight: .light)
                print("⚠️ 无法创建ADELE字体，回退到SF字体")
            }
        } else if selectedEnglishFontName == "Quantico" {
            // 直接使用选中的字体粗细名称
            if !selectedEnglishFontWeight.isEmpty {
                let quanticoFontName = "Quantico-" + selectedEnglishFontWeight.replacingOccurrences(of: " ", with: "")
                
                // 尝试创建Quantico字体
                if let font = UIFont(name: quanticoFontName, size: fontSize) {
                    englishFont = font
                    print("✅ 应用Quantico英文字体: \(quanticoFontName), 大小: \(fontSize)")
                } else {
                    // 如果无法创建Quantico字体，回退到使用SF字体
                    englishFont = UIFont.systemFont(ofSize: fontSize, weight: .regular)
                    print("⚠️ 无法创建Quantico字体，回退到SF字体")
                }
            } else {
                // 如果没有选择字体粗细，使用默认Regular
                let quanticoFontName = "Quantico-Regular"
                if let font = UIFont(name: quanticoFontName, size: fontSize) {
                    englishFont = font
                    print("✅ 应用默认Quantico-Regular英文字体, 大小: \(fontSize)")
                } else {
                    englishFont = UIFont.systemFont(ofSize: fontSize, weight: .regular)
                    print("⚠️ 无法创建默认Quantico字体，回退到SF字体")
                }
            }
        } else if selectedEnglishFontName == "Syntax" {
            // 直接使用选中的字体粗细名称
            if !selectedEnglishFontWeight.isEmpty {
                let syntaxFontName = "Syntax-" + selectedEnglishFontWeight
                
                // 尝试创建Syntax字体
                if let font = UIFont(name: syntaxFontName, size: fontSize) {
                    englishFont = font
                    print("✅ 应用Syntax英文字体: \(syntaxFontName), 大小: \(fontSize)")
                } else {
                    // 如果无法创建Syntax字体，回退到使用SF字体
                    englishFont = UIFont.systemFont(ofSize: fontSize, weight: .regular)
                    print("⚠️ 无法创建Syntax字体，回退到SF字体")
                }
            } else {
                // 如果没有选择字体粗细，使用默认Roman
                let syntaxFontName = "Syntax-Roman"
                if let font = UIFont(name: syntaxFontName, size: fontSize) {
                    englishFont = font
                    print("✅ 应用默认Syntax-Roman英文字体, 大小: \(fontSize)")
                } else {
                    englishFont = UIFont.systemFont(ofSize: fontSize, weight: .regular)
                    print("⚠️ 无法创建默认Syntax字体，回退到SF字体")
                }
            }
        } else if selectedEnglishFontName == "Makinas-Flat" {
            if let font = UIFont(name: "Makinas-4-Flat", size: fontSize) {
                englishFont = font
                print("✅ 应用Makinas-Flat英文字体, 大小: \(fontSize)")
            } else {
                englishFont = UIFont.systemFont(ofSize: fontSize, weight: .regular)
                print("⚠️ 无法创建Makinas-Flat字体，回退到SF字体")
            }
        } else if selectedEnglishFontName == "Makinas-Square" {
            if let font = UIFont(name: "Makinas-4-Square", size: fontSize) {
                englishFont = font
                print("✅ 应用Makinas-Square英文字体, 大小: \(fontSize)")
            } else {
                englishFont = UIFont.systemFont(ofSize: fontSize, weight: .regular)
                print("⚠️ 无法创建Makinas-Square字体，回退到SF字体")
            }
        } else {
            // 如果没有选择特定的英文字体，使用系统SF字体
            englishFont = UIFont.systemFont(ofSize: fontSize, weight: .regular)
            if selectedEnglishFontName.isEmpty {
                print("ℹ️ 使用默认SF字体，未选择英文字体")
            } else {
                print("⚠️ 未知的英文字体: \(selectedEnglishFontName)，使用SF字体")
            }
        }
        
        // 遍历文本，为英文和数字应用英文字体，为中文应用中文字体
        for (i, char) in text.enumerated() {
            if char.isASCII {
                // 只有在用户选择了英文字体时才应用英文字体
                if !selectedEnglishFontName.isEmpty {
                    // 英文、数字和符号使用英文字体
                    attributedString.addAttribute(.font, value: englishFont, range: NSRange(location: i, length: 1))
                }
                // 否则，使用原始字体（中文字体）处理英文字符
            } else {
                // 中文使用中文字体
                attributedString.addAttribute(.font, value: chineseFont, range: NSRange(location: i, length: 1))
            }
        }
        
        // 应用富文本
        label.attributedText = attributedString
    }
    
    // 新增辅助方法：映射Futura PT的特殊命名规则
    private static func mapFuturaPTWeight(_ weight: String) -> String {
        switch weight {
        case "Light": return "Light"
        case "Light Obl": return "LightObl"
        case "Book": return "Book"
        case "Book Obl": return "BookObl"
        case "Medium": return "Medium"
        case "Medium Obl": return "MediumObl"
        case "Demi": return "Demi"
        case "Demi Obl": return "DemiObl"
        default: return "Book" // 默认使用Book变体
        }
    }

    /// 应用描述选项文字颜色逻辑，使其与间隔符的颜色逻辑保持一致
    /// - Parameters:
    ///   - label: 要应用颜色的标签
    ///   - settings: 水印设置
    ///   - borderColor: 边框颜色
    ///   - styleIdentifier: 样式标识符，用于日志
    static func applyDescriptionTextColor(to label: UILabel, with settings: WatermarkSettings, borderColor: UIColor, styleIdentifier: String = "WatermarkStyle") {
        // 获取适当的颜色
        label.textColor = getSpecialElementColor(with: settings, borderColor: borderColor)
        print("✅ \(styleIdentifier): 应用适当颜色到描述选项文字")
    }
    
    /// 获取特殊元素颜色（用于描述选项文字和分隔符）
    /// - Parameters:
    ///   - settings: 水印设置
    ///   - borderColor: 边框颜色
    /// - Returns: 适当的颜色
    static func getSpecialElementColor(with settings: WatermarkSettings, borderColor: UIColor) -> UIColor {
        // 获取字体颜色
        let fontColor = UIColor(
            red: CGFloat(settings.fontColorRed),
            green: CGFloat(settings.fontColorGreen),
            blue: CGFloat(settings.fontColorBlue),
            alpha: CGFloat(settings.fontColorAlpha)
        )
        
        // 判断字体颜色是否为黑色，边框颜色是否为白色
        let isBlackFont = settings.fontColorRed == 0 && settings.fontColorGreen == 0 && settings.fontColorBlue == 0
        let isWhiteBorder = borderColor == WatermarkConstants.Colors.borderWhite
        
        // 确定颜色
        if isWhiteBorder && isBlackFont {
            // 如果边框是白色且字体是黑色，使用系统分隔符颜色
            return UIColor.separator
        } else {
            // 其他情况使用字体颜色
            return fontColor
        }
    }
}

// MARK: - 边框样式工具类
/// 提供边框样式共享的工具方法
class BorderStyleUtils {
    /// 计算边框颜色
    static func getBorderColor(from settings: WatermarkSettings) -> UIColor {
        // 根据边框颜色的RGB值判断使用哪个颜色常量
        if settings.borderColorRed == 1 && settings.borderColorGreen == 1 && settings.borderColorBlue == 1 {
            // 白色边框
            return WatermarkConstants.Colors.borderWhite
        } else if settings.borderColorRed == 0 && settings.borderColorGreen == 0 && settings.borderColorBlue == 0 {
            // 纯黑色边框 (#000000)
            return UIColor.black
        } else if (settings.borderColorRed < 0.1 && settings.borderColorGreen < 0.1 && settings.borderColorBlue < 0.1) &&
                  !(settings.borderColorRed == 0 && settings.borderColorGreen == 0 && settings.borderColorBlue == 0) {
            // 黑色边框 (#191a1b) - 仅当不是纯黑色时
            return WatermarkConstants.Colors.borderBlack
        } else if abs(settings.borderColorRed - 83/255.0) < 0.01 && abs(settings.borderColorGreen - 83/255.0) < 0.01 && abs(settings.borderColorBlue - 83/255.0) < 0.01 {
            // 灰色边框 (#535353)
            return WatermarkConstants.Colors.borderGray
        } else {
            // 其他自定义颜色（包括新增的橙色、红色、黄色、绿色、蓝色、紫色等）
            return UIColor(
                red: CGFloat(settings.borderColorRed),
                green: CGFloat(settings.borderColorGreen),
                blue: CGFloat(settings.borderColorBlue),
                alpha: CGFloat(settings.borderColorAlpha)
            )
        }
    }
    
    /// 计算基础边框宽度
    static func getBaseBorderWidth(from settings: WatermarkSettings) -> CGFloat {
        let minBorderWidthPercentage: CGFloat = WatermarkConstants.Factory.minBorderWidthScreenHeightFactor
        let maxBorderWidthPercentage: CGFloat = WatermarkConstants.Factory.maxBorderWidthScreenHeightFactor
        
        // 分段计算：
        // 1. 当滑块值为0-0.2时，线性映射到0-0.01(0%-1%)
        // 2. 当滑块值为0.2-1.0时，线性映射到0.01-0.05(1%-5%)
        let multiplier = CGFloat(settings.borderThicknessMultiplier) // 范围应为0.0-1.0
        let targetWidth: CGFloat
        
        if multiplier <= 0.2 {
            // 0-20%的滑块值映射到0-1%的屏幕高度
            targetWidth = minBorderWidthPercentage + (multiplier / 0.2) * 0.01
        } else {
            // 20%-100%的滑块值映射到1%-5%的屏幕高度
            let remainingMultiplier = (multiplier - 0.2) / 0.8 // 归一化到0-1范围
            targetWidth = 0.01 + remainingMultiplier * (maxBorderWidthPercentage - 0.01)
        }
        
        return UIScreen.main.bounds.height * targetWidth
    }
    
    /// 计算水印1的上下边框宽度
    static func getTopBottomBorderWidth(from settings: WatermarkSettings, baseBorderWidth: CGFloat) -> CGFloat {
        let topBottomMultiplier = CGFloat(settings.topBottomBorderThicknessMultiplier)
        let topBottomMinWidthPercentage: CGFloat = 0.0 // 0%屏幕高度
        let topBottomMaxWidthPercentage: CGFloat = 0.1  // 10%屏幕高度
        let topBottomExtraWidth = UIScreen.main.bounds.height * (topBottomMinWidthPercentage + (topBottomMultiplier * (topBottomMaxWidthPercentage - topBottomMinWidthPercentage)))
        
        return baseBorderWidth + topBottomExtraWidth
    }
    
    /// 创建边框
    static func createBorders(for container: UIView, 
                             topWidth: CGFloat, 
                             bottomWidth: CGFloat, 
                             leftWidth: CGFloat, 
                             rightWidth: CGFloat, 
                             color: UIColor) -> (top: UIView, bottom: UIView, left: UIView, right: UIView) {
        let containerWidth = container.bounds.width
        let containerHeight = container.bounds.height
        
        // 创建上边框
        let topBorder = UIView(frame: CGRect(x: 0, y: 0, width: containerWidth, height: topWidth))
        topBorder.backgroundColor = color
        container.addSubview(topBorder)
        
        // 创建底边框
        let bottomBorder = UIView(frame: CGRect(x: 0, y: containerHeight - bottomWidth, width: containerWidth, height: bottomWidth))
        bottomBorder.backgroundColor = color
        container.addSubview(bottomBorder)
        
        // 创建左边框
        let leftBorder = UIView(frame: CGRect(x: 0, y: topWidth, width: leftWidth, height: containerHeight - topWidth - bottomWidth))
        leftBorder.backgroundColor = color
        container.addSubview(leftBorder)
        
        // 创建右边框
        let rightBorder = UIView(frame: CGRect(x: containerWidth - rightWidth, y: topWidth, width: rightWidth, height: containerHeight - topWidth - bottomWidth))
        rightBorder.backgroundColor = color
        container.addSubview(rightBorder)
        
        return (topBorder, bottomBorder, leftBorder, rightBorder)
    }
}

// MARK: - 偏好选项工具类
/// 提供偏好选项共享的工具方法
class PreferenceUtils {
    /// 根据选项生成偏好文本
    static func getPreferenceText(for option: String, with settings: WatermarkSettings? = nil) -> String {
        let defaultSettings = WatermarkSettingsManager.shared.getSettings()
        let settingsToUse = settings ?? defaultSettings
        
        switch option {
        case "参数":
            return settingsToUse.preferenceParametersText.isEmpty ? 
                   "50mm f/1.8 1/125s ISO100" : settingsToUse.preferenceParametersText
        case "经纬度":
            return settingsToUse.preferenceLocationText.isEmpty ? 
                   "39.9042° N, 116.4074° E" : settingsToUse.preferenceLocationText
        case "日期":
            if !settingsToUse.preferenceDateText.isEmpty {
                return settingsToUse.preferenceDateText
            }
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy-MM-dd"
            return dateFormatter.string(from: Date())
        case "位置":
            return settingsToUse.preferencePlaceText.isEmpty ? 
                   "北京市海淀区" : settingsToUse.preferencePlaceText
        case "日期&位置":
            // 这个选项会在createPreferenceLabel中特殊处理，这里返回空字符串
            return ""
        default:
            return ""
        }
    }
    
    /// 计算字体大小
    static func getFontSize(enabledElementsCount: Int, customFontSizeFactors: (single: CGFloat, two: CGFloat, three: CGFloat)? = nil, fixedFontSize: CGFloat? = nil) -> CGFloat {
        if let size = fixedFontSize {
            // 使用固定大小
            return size
        } else if let factors = customFontSizeFactors {
            // 使用自定义字体大小因子
            if enabledElementsCount == 1 {
                return UIScreen.main.bounds.height * factors.single
            } else if enabledElementsCount == 2 {
                return UIScreen.main.bounds.height * factors.two
            } else {
                return UIScreen.main.bounds.height * factors.three
            }
        } else {
            // 使用默认字体大小
            return UIScreen.main.bounds.height * 0.02
        }
    }
    
    /// 创建偏好标签
    static func createPreferenceLabel(for option: String, with settings: WatermarkSettings, enabledElementsCount: Int = 3, customFontSizeFactors: (single: CGFloat, two: CGFloat, three: CGFloat)? = nil, fixedFontSize: CGFloat? = nil) -> UILabel {
        // 特殊处理"日期&位置"选项
        if option == "日期&位置" {
            return createDateLocationLabel(with: settings, enabledElementsCount: enabledElementsCount, customFontSizeFactors: customFontSizeFactors, fixedFontSize: fixedFontSize)
        }
        
        // 检查是否应该使用新的多选模式
        if !settings.selectedPreferences.isEmpty {
            // 使用新的多选API
            return createMultiplePreferencesLabel(with: settings, enabledElementsCount: enabledElementsCount, customFontSizeFactors: customFontSizeFactors, fixedFontSize: fixedFontSize)
        }
        
        let label = UILabel()
        
        // 设置文本
        label.text = getPreferenceText(for: option, with: settings)
        label.textAlignment = .center
        
        // 计算字体大小
        let fontSize = getFontSize(enabledElementsCount: enabledElementsCount, customFontSizeFactors: customFontSizeFactors, fixedFontSize: fixedFontSize)
        
        // 应用字体样式
        WatermarkStyleUtils.applyFontStyle(to: label, with: settings, fontSize: fontSize, styleIdentifier: "PreferenceUtils")
        
        return label
    }
    
    /// 创建多选偏好标签
    static func createMultiplePreferencesLabel(with settings: WatermarkSettings, enabledElementsCount: Int = 3, customFontSizeFactors: (single: CGFloat, two: CGFloat, three: CGFloat)? = nil, fixedFontSize: CGFloat? = nil) -> UILabel {
        let label = UILabel()
        
        // 计算字体大小
        let fontSize = getFontSize(enabledElementsCount: enabledElementsCount, customFontSizeFactors: customFontSizeFactors, fixedFontSize: fixedFontSize)
        
        // 创建字体颜色
        let fontColor = UIColor(
            red: CGFloat(settings.fontColorRed),
            green: CGFloat(settings.fontColorGreen),
            blue: CGFloat(settings.fontColorBlue),
            alpha: CGFloat(settings.fontColorAlpha)
        )
        
        // 获取正确的字体 - 使用与applyFontStyle相同的字体选择逻辑
        let fontThickness = settings.fontThicknessMultiplier
        var fontWeight = UIFont.Weight.regular
        
        if fontThickness < 0.25 {
            fontWeight = .light
        } else if fontThickness < 0.5 {
            fontWeight = .regular
        } else if fontThickness < 0.75 {
            fontWeight = .semibold
        } else {
            fontWeight = .bold
        }
        
        // 根据用户选择的字体名称确定实际字体
        let fontName = settings.selectedFontName
        var actualFont: UIFont
        
        switch fontName {
        case "黑体":
            if let customFont = UIFont(name: WatermarkConstants.Common.defaultPingFangSCSemiboldFont, size: fontSize) {
                actualFont = customFont
            } else {
                actualFont = UIFont.systemFont(ofSize: fontSize, weight: fontWeight)
            }
        case "苹方":
            if let customFont = UIFont(name: WatermarkConstants.Common.defaultPingFangSCRegularFont, size: fontSize) {
                actualFont = customFont
            } else {
                actualFont = UIFont.systemFont(ofSize: fontSize, weight: fontWeight)
            }
        case "Times":
            if let customFont = UIFont(name: WatermarkConstants.Common.defaultTimesNewRomanFont, size: fontSize) {
                actualFont = customFont
            } else {
                actualFont = UIFont.systemFont(ofSize: fontSize, weight: fontWeight)
            }
        case "Courier":
            if let customFont = UIFont(name: WatermarkConstants.Common.defaultCourierNewFont, size: fontSize) {
                actualFont = customFont
            } else {
                actualFont = UIFont.systemFont(ofSize: fontSize, weight: fontWeight)
            }
        case "HarmonyOS_Sans_SC":
            // 直接根据选择的字重使用对应的字体文件
            let selectedWeight = settings.selectedFontWeight
            var harmonyFontName = "HarmonyOS_Sans_SC_Regular" // 默认使用Regular
            
            switch selectedWeight {
                case "Thin": harmonyFontName = "HarmonyOS_Sans_SC_Thin"
                case "Light": harmonyFontName = "HarmonyOS_Sans_SC_Light"
                case "Regular": harmonyFontName = "HarmonyOS_Sans_SC_Regular"
                case "Medium": harmonyFontName = "HarmonyOS_Sans_SC_Medium"
                case "Bold": harmonyFontName = "HarmonyOS_Sans_SC_Bold"
                case "Black": harmonyFontName = "HarmonyOS_Sans_SC_Black"
                default: harmonyFontName = "HarmonyOS_Sans_SC_Regular"
            }
            
            if let font = UIFont(name: harmonyFontName, size: fontSize) {
                actualFont = font
            } else {
                // 转换selectedWeight到UIFont.Weight
                let systemWeight = WatermarkStyleUtils.getSystemFontWeight(from: selectedWeight)
                actualFont = UIFont.systemFont(ofSize: fontSize, weight: systemWeight)
            }
        case "PingFang-SC":
            // 直接根据选择的字重使用对应的字体文件
            let selectedWeight = settings.selectedFontWeight
            var pingFangFontName = "PingFangSC-Regular" // 默认使用Regular
            
            switch selectedWeight {
                case "Ultralight": pingFangFontName = "PingFangSC-Ultralight"
                case "Thin": pingFangFontName = "PingFangSC-Thin"
                case "Light": pingFangFontName = "PingFangSC-Light"
                case "Regular": pingFangFontName = "PingFangSC-Regular"
                case "Medium": pingFangFontName = "PingFangSC-Medium"
                case "Semibold": pingFangFontName = "PingFangSC-Semibold"
                default: pingFangFontName = "PingFangSC-Regular"
            }
            
            if let pingFangFont = UIFont(name: pingFangFontName, size: fontSize) {
                actualFont = pingFangFont
            } else {
                // 转换selectedWeight到UIFont.Weight
                let systemWeight = WatermarkStyleUtils.getSystemFontWeight(from: selectedWeight)
                actualFont = UIFont.systemFont(ofSize: fontSize, weight: systemWeight)
            }
        case "SourceHanSansSC":
            // 直接根据选择的字重使用对应的字体文件
            let selectedWeight = settings.selectedFontWeight
            var sourceHanFontName = "SourceHanSansSC-Regular" // 默认使用Regular
            
            switch selectedWeight {
                case "ExtraLight": sourceHanFontName = "SourceHanSansSC-ExtraLight"
                case "Light": sourceHanFontName = "SourceHanSansSC-Light"
                case "Normal": sourceHanFontName = "SourceHanSansSC-Normal"
                case "Regular": sourceHanFontName = "SourceHanSansSC-Regular"
                case "Medium": sourceHanFontName = "SourceHanSansSC-Medium"
                case "Bold": sourceHanFontName = "SourceHanSansSC-Bold"
                case "Heavy": sourceHanFontName = "SourceHanSansSC-Heavy"
                default: sourceHanFontName = "SourceHanSansSC-Regular"
            }
            
            if let sourceHanFont = UIFont(name: sourceHanFontName, size: fontSize) {
                actualFont = sourceHanFont
            } else {
                // 转换selectedWeight到UIFont.Weight
                let systemWeight = WatermarkStyleUtils.getSystemFontWeight(from: selectedWeight)
                actualFont = UIFont.systemFont(ofSize: fontSize, weight: systemWeight)
            }
        case "HONORSansCN":
            // 直接根据选择的字重使用对应的字体文件
            let selectedWeight = settings.selectedFontWeight
            var honorFontName = "HONORSansCN-Regular" // 默认使用Regular
            
            switch selectedWeight {
                case "Thin": honorFontName = "HONORSansCN-Thin"
                case "ExtraLight": honorFontName = "HONORSansCN-ExtraLight"
                case "Light": honorFontName = "HONORSansCN-Light"
                case "Regular": honorFontName = "HONORSansCN-Regular"
                case "Medium": honorFontName = "HONORSansCN-Medium"
                case "SemiBold": honorFontName = "HONORSansCN-SemiBold"
                case "Bold": honorFontName = "HONORSansCN-Bold"
                case "ExtraBold": honorFontName = "HONORSansCN-ExtraBold"
                case "Black": honorFontName = "HONORSansCN-Black"
                default: honorFontName = "HONORSansCN-Regular"
            }
            
            if let honorFont = UIFont(name: honorFontName, size: fontSize) {
                actualFont = honorFont
            } else {
                // 转换selectedWeight到UIFont.Weight
                let systemWeight = WatermarkStyleUtils.getSystemFontWeight(from: selectedWeight)
                actualFont = UIFont.systemFont(ofSize: fontSize, weight: systemWeight)
            }
        // Makinas字体已移至英文字体选项
        default:
            // 默认使用regular字重
            actualFont = UIFont.systemFont(ofSize: fontSize, weight: .regular)
        }
        
        // 创建属性字符串
        let attributedString = NSMutableAttributedString()
        
        // 检查是否启用分行显示
        let isMultilineEnabled = settings.isMultilinePreferenceEnabled
        
        // 使用多选项数组生成文本
        for (index, prefOption) in settings.selectedPreferences.enumerated() {
            let text = getPreferenceText(for: prefOption, with: settings)
            
            // 创建当前文本的属性 - 使用正确的字体
            let textAttributes: [NSAttributedString.Key: Any] = [
                .font: actualFont,
                .foregroundColor: fontColor
            ]
            
            // 添加文本
            attributedString.append(NSAttributedString(string: text, attributes: textAttributes))
            
            // 如果不是最后一个选项，根据分行设置添加空格或换行
            if index < settings.selectedPreferences.count - 1 {
                if isMultilineEnabled {
                    // 分行显示 - 添加换行符
                    attributedString.append(NSAttributedString(string: "\n", attributes: textAttributes))
                } else {
                    // 单行显示 - 添加空格间距
                    let spaceAttributes: [NSAttributedString.Key: Any] = [
                        .font: actualFont,
                        .foregroundColor: fontColor,
                        .kern: UIScreen.main.bounds.width * 0.01 // 控制间距，为1%屏幕宽度
                    ]
                    attributedString.append(NSAttributedString(string: " ", attributes: spaceAttributes))
                }
            }
        }
        
        // 设置富文本
        label.attributedText = attributedString
        label.textAlignment = .center
        label.numberOfLines = 0
        label.lineBreakMode = .byWordWrapping
        
        return label
    }
    
    /// 创建日期&位置标签
    static func createDateLocationLabel(with settings: WatermarkSettings, enabledElementsCount: Int = 3, customFontSizeFactors: (single: CGFloat, two: CGFloat, three: CGFloat)? = nil, fixedFontSize: CGFloat? = nil) -> UILabel {
        let label = UILabel()
        
        // 获取日期
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let dateText = dateFormatter.string(from: Date())
        
        // 获取位置
        let locationText = "北京市海淀区"
        
        // 计算字体大小
        let fontSize = getFontSize(enabledElementsCount: enabledElementsCount, customFontSizeFactors: customFontSizeFactors, fixedFontSize: fixedFontSize)
        
        // 创建属性字符串
        let attributedString = NSMutableAttributedString()
        
        // 创建字体颜色
        let fontColor = UIColor(
            red: CGFloat(settings.fontColorRed),
            green: CGFloat(settings.fontColorGreen),
            blue: CGFloat(settings.fontColorBlue),
            alpha: CGFloat(settings.fontColorAlpha)
        )
        
        // 添加日期文本
        let dateAttributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: fontSize),
            .foregroundColor: fontColor
        ]
        attributedString.append(NSAttributedString(string: dateText, attributes: dateAttributes))
        
        // 添加间距（使用空格）
        // 计算间距宽度为屏幕宽度的1%
        let screenWidth = UIScreen.main.bounds.width
        let spacingWidth = screenWidth * 0.01
        
        // 创建空格属性字符串，设置kern属性来控制间距
        let spaceAttributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: fontSize),
            .kern: spacingWidth // 使用kern属性控制字符间距
        ]
        attributedString.append(NSAttributedString(string: " ", attributes: spaceAttributes))
        
        // 添加位置文本
        let locationAttributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: fontSize),
            .foregroundColor: fontColor
        ]
        attributedString.append(NSAttributedString(string: locationText, attributes: locationAttributes))
        
        // 设置属性字符串
        label.attributedText = attributedString
        
        // 应用字体样式（基础设置）
        WatermarkStyleUtils.applyFontStyle(to: label, with: settings, fontSize: fontSize, styleIdentifier: "PreferenceUtils.DateLocation")
        
        return label
    }
}

// MARK: - 文字工具类
/// 提供文字输入框和署名共享的工具方法
class TextUtils {
    /// 计算字体大小
    static func getFontSize(enabledElementsCount: Int, customFontSizeFactors: (single: CGFloat, two: CGFloat, three: CGFloat)? = nil, fixedFontSize: CGFloat? = nil) -> CGFloat {
        if let size = fixedFontSize {
            // 使用固定大小
            return size
        } else if let factors = customFontSizeFactors {
            // 使用自定义大小因子
            if enabledElementsCount == 1 {
                return UIScreen.main.bounds.height * factors.single
            } else if enabledElementsCount == 2 {
                return UIScreen.main.bounds.height * factors.two
            } else {
                return UIScreen.main.bounds.height * factors.three
            }
        } else {
            // 使用默认大小
            return UIScreen.main.bounds.height * 0.02
        }
    }
    
    /// 创建水印文字标签
    static func createTextLabel(with settings: WatermarkSettings, text: String? = nil, enabledElementsCount: Int = 3, customFontSizeFactors: (single: CGFloat, two: CGFloat, three: CGFloat)? = nil, fixedFontSize: CGFloat? = nil) -> UILabel {
        let label = UILabel()
        // 如果提供了自定义文本，则使用它，否则使用设置中的watermarkText
        label.text = text ?? settings.watermarkText
        label.textAlignment = .center
        
        // 计算字体大小
        var fontSize = getFontSize(enabledElementsCount: enabledElementsCount, customFontSizeFactors: customFontSizeFactors, fixedFontSize: fixedFontSize)
        
        // 应用文字大小乘数
        fontSize *= CGFloat(settings.textFontSizeMultiplier)
        
        // 应用字体样式，明确标记为文字类型
        WatermarkStyleUtils.applyFontStyle(to: label, with: settings, fontSize: fontSize, styleIdentifier: "TextUtils.Text", isSignature: false)
        
        return label
    }
    
    /// 创建署名标签
    static func createSignatureLabel(with settings: WatermarkSettings, fontSize: CGFloat) -> UILabel {
        let label = UILabel()
        label.text = settings.watermarkSignature
        label.textAlignment = .center
        
        // 应用字体样式，标记为署名类型
        WatermarkStyleUtils.applyFontStyle(to: label, with: settings, fontSize: fontSize, styleIdentifier: "TextUtils.Signature", isSignature: true)
        
        return label
    }
}

// MARK: - Logo工具类
/// 提供Logo相关的工具方法
class LogoUtils {
    
    /// 获取Logo图像
    static func getLogoImage(for logoName: String) -> (image: UIImage?, tintColor: UIColor?) {
        // 支持颜色调整的Logo列表
        let colorAdjustableLogos = [
            "apple.logo",
            "LH Apple",
            "LH Canon",
            "LH DJI",
            "LH OSMO Action", "LH OSMO Action-2", "LH OSMO Action-3", "LH OSMO Action-4", "LH OSMO Action-5pro",
            "LH Air-3s", "LH Air-3", "LH Avata", "LH Avata-2", "LH Inspire",
            "LH Mavic-3", "LH Mavic-3c", "LH Mavic-3pro", "LH Mavic-4pro",
            "LH Mini-3", "LH Mini-4pro", "LH Mini-4k",
            "LH Mobile-6", "LH Mobile-7",
            "LH Pocket-3", "LH Pocket-2",
            "LH RONIN",
            "LH Noe", "LH Flip",
            "LH Hasselblad",
            "LH Lomo",
            "LH Lumix",
            "LH RICOH",
            "LH Samsung",
            "LH Sony",
            "LH pentax",
            "LH Olympus",
            "LH Konica",
            "LH Minolta",
            "LH Contax",
            "LH Mamiya",
            "LH Sigma",
            "LH Rollei",
            "LH H",
            "LH polaroid-1",
            "LH Leica-2",
            "LH Nikon-2",
            "LH Z",
            "LH Z-2",
            "LH Huawei-3"   // 添加 LH Huawei-3 到可调整颜色的Logo列表
        ]
        
        // 获取用户设置的Logo颜色
        let settings = WatermarkSettingsManager.shared.getSettings()
        let logoColor = UIColor(
            red: CGFloat(settings.logoColorRed),
            green: CGFloat(settings.logoColorGreen),
            blue: CGFloat(settings.logoColorBlue),
            alpha: CGFloat(settings.logoColorAlpha)
        )
        
        // 检查是否启用颜色调整
        // 灰色表示不使用颜色调整（R、G、B值都接近0.5且非常接近）
        let isColorAdjustmentDisabled = abs(settings.logoColorRed - 0.5) < 0.1 && 
                                       abs(settings.logoColorGreen - 0.5) < 0.1 && 
                                       abs(settings.logoColorBlue - 0.5) < 0.1
        
        // 特殊处理 apple.logo
        if logoName == "apple.logo" {
            let appLogoImage = UIImage(systemName: "apple.logo")
            
            let tintColor: UIColor
            if isColorAdjustmentDisabled {
                // 使用默认黑色
                tintColor = .black
            } else {
                // 使用设置中的颜色
                tintColor = logoColor
            }
            
            return (appLogoImage, tintColor)
        }
        
        // 一般Logo处理
        let logoImage = UIImage(named: logoName) // 从应用Bundle中加载图像
        
        // 检查是否需要应用颜色调整（在支持列表中且已启用颜色调整）
        let shouldApplyColorAdjustment = colorAdjustableLogos.contains(logoName) && !isColorAdjustmentDisabled
        
        if shouldApplyColorAdjustment {
            // 设置为template模式，使其响应tintColor
            return (logoImage?.withRenderingMode(.alwaysTemplate), logoColor)
        } else {
            return (logoImage, nil) // 不需要调整颜色
        }
    }
    
    static func getLogoSize(enabledElementsCount: Int, customSizeFactors: (single: CGFloat, two: CGFloat, three: CGFloat)? = nil, fixedSize: CGFloat? = nil) -> CGFloat {
        if let size = fixedSize {
            // 使用固定大小
            return size
        } else if let factors = customSizeFactors {
            // 使用自定义大小因子
            if enabledElementsCount == 1 {
                return UIScreen.main.bounds.height * factors.single
            } else if enabledElementsCount == 2 {
                return UIScreen.main.bounds.height * factors.two
            } else {
                return UIScreen.main.bounds.height * factors.three
            }
        } else {
            // 使用默认大小
            return UIScreen.main.bounds.height * 0.02
        }
    }
    
    /// 创建Logo图像视图 - 传入特定的Logo名称
    static func createLogoImageView(logoName: String, with settings: WatermarkSettings, enabledElementsCount: Int = 3, customSizeFactors: (single: CGFloat, two: CGFloat, three: CGFloat)? = nil, fixedSize: CGFloat? = nil) -> UIImageView {
        let logoImageView = UIImageView()
        logoImageView.contentMode = .scaleAspectFit
        
        // 获取Logo图像和颜色
        let (image, tintColor) = getLogoImage(for: logoName)
        logoImageView.image = image
        if let tintColor = tintColor {
            logoImageView.tintColor = tintColor
        }
        
        // 计算Logo大小
        let logoHeight = getLogoSize(enabledElementsCount: enabledElementsCount, customSizeFactors: customSizeFactors, fixedSize: fixedSize)
        
        // 应用Logo大小乘数
        let finalLogoHeight = logoHeight * CGFloat(settings.logoSizeMultiplier)
        
        // 计算宽度，保持原始比例
        if let image = logoImageView.image {
            let aspectRatio = image.size.width / image.size.height
            let logoWidth = finalLogoHeight * aspectRatio
            logoImageView.frame.size = CGSize(width: logoWidth, height: finalLogoHeight)
        } else {
            // 如果没有图片，使用正方形
            logoImageView.frame.size = CGSize(width: finalLogoHeight, height: finalLogoHeight)
        }
        
        return logoImageView
    }
    
    /// 创建Logo图像视图 - 使用settings.selectedLogo
    static func createLogoImageView(with settings: WatermarkSettings, enabledElementsCount: Int = 3, customSizeFactors: (single: CGFloat, two: CGFloat, three: CGFloat)? = nil, fixedSize: CGFloat? = nil) -> UIImageView {
        return createLogoImageView(logoName: settings.selectedLogo, with: settings, enabledElementsCount: enabledElementsCount, customSizeFactors: customSizeFactors, fixedSize: fixedSize)
    }
}

// MARK: - 水印工具扩展
/// 提供水印样式共享的工具方法
extension WatermarkStyle {
    // 这里不再需要任何方法，因为我们已经将它们移到了 WatermarkStyleUtils 类中
}

// MARK: - 水印样式工厂

// MARK: - 水印样式工厂
/// 水印样式工厂 - 根据选择创建不同的水印样式
class WatermarkStyleFactory {
    // 边框宽度百分比常量已移至 BorderStyleUtils 类

    /// 创建水印样式
    static func createWatermarkStyle(type: String, settings: WatermarkSettings) -> WatermarkStyle? {
        // 使用BorderStyleUtils获取边框颜色
        let commonBorderColor = BorderStyleUtils.getBorderColor(from: settings)

        // 从设置中统一读取字体颜色
        let commonFontColor = UIColor(
            red: CGFloat(settings.fontColorRed),
            green: CGFloat(settings.fontColorGreen),
            blue: CGFloat(settings.fontColorBlue),
            alpha: CGFloat(settings.fontColorAlpha)
        )
        
        // 获取字体设置
        let fontName = settings.selectedFontName
        let fontThicknessMultiplier = CGFloat(settings.fontThicknessMultiplier)
        // 根据字体名称选择实际的系统字体
        var actualFontName: String
        switch fontName {
        case "黑体":
            actualFontName = WatermarkConstants.Common.defaultPingFangSCSemiboldFont
        case "苹方":
            actualFontName = WatermarkConstants.Common.defaultPingFangSCRegularFont
        case "Times":
            actualFontName = WatermarkConstants.Common.defaultTimesNewRomanFont
        case "Courier":
            actualFontName = WatermarkConstants.Common.defaultCourierNewFont
        case "HarmonyOS_Sans_SC":
            // 对于HarmonyOS字体，我们使用Regular变体作为默认选项
            actualFontName = "HarmonyOS_Sans_SC_Regular"
        case "PingFang-SC":
            // 对于PingFang-SC字体，我们使用Regular变体作为默认选项
            actualFontName = "PingFangSC-Regular"
        case "SourceHanSansSC":
            // 对于SourceHanSansSC字体，我们使用Regular变体作为默认选项
            actualFontName = "SourceHanSansSC-Regular"
        case "HONORSansCN":
            // 对于HONORSansCN字体，我们使用Regular变体作为默认选项  
            actualFontName = "HONORSansCN-Regular"
        case "Makinas-Flat":
            actualFontName = "Makinas-Flat"
        case "Makinas-Square":
            actualFontName = "Makinas-Square"
        default: // 系统
            actualFontName = WatermarkConstants.Common.defaultSystemFont
        }

        // 使用BorderStyleUtils获取基础边框宽度
        let actualBorderWidth = BorderStyleUtils.getBaseBorderWidth(from: settings)

        switch type {
        case "border_2percent": 
            // 使用BorderStyleUtils获取边框宽度
            let baseBorderWidth = actualBorderWidth
            let topBottomWidth = BorderStyleUtils.getTopBottomBorderWidth(from: settings, baseBorderWidth: baseBorderWidth)
            let sideWidth = baseBorderWidth
            
            return CustomWatermarkStyle1(
                borderWidth: sideWidth, 
                topBottomBorderWidth: topBottomWidth, 
                borderColor: commonBorderColor
            )
        
        case "polaroid":
            // 拍立得的上/左/右边框使用计算出的边框宽度
            let sideTopWidth = actualBorderWidth
            // 使用宽边框粗细设置计算底部边框宽度
            let bottomWidth: CGFloat = UIScreen.main.bounds.height * WideBorderThicknessUtils.mapSliderValueToActualThickness(
                sliderValue: settings.wideBorderThicknessMultiplier, 
                watermarkType: "polaroid"
            )
            
            return CustomWatermarkStyle2(topBorderWidth: sideTopWidth, 
                                        bottomBorderWidth: bottomWidth,
                                        sideBorderWidth: sideTopWidth,
                                        borderColor: commonBorderColor)
        
        case "film":
            // 胶片风格示例：如果它也使用粗细调节，可以这样：
            // let filmBorderWidth = actualBorderWidth 
            // return CustomWatermarkStyle3(borderWidth: filmBorderWidth, borderColor: commonBorderColor) 
            // 或者，如果它有固定的设计，则不使用 actualBorderWidth
            let filmFixedBorder = UIScreen.main.bounds.height * WatermarkConstants.Film.fixedBorderScreenHeightFactor // 使用常量
            return CustomWatermarkStyle3(borderWidth: filmFixedBorder, borderColor: WatermarkConstants.Film.borderColor) // 使用常量定义的颜色
            
        case "custom17":
            // 自定义水印17，基于胶片风格(水印3)，但使用独立常量
            let custom17FixedBorder = UIScreen.main.bounds.height * WatermarkConstants.Custom17.fixedBorderScreenHeightFactor
            return CustomWatermarkStyle17(borderWidth: custom17FixedBorder, borderColor: WatermarkConstants.Custom17.borderColor)
            
        case "custom18":
            // 自定义水印18，基于简约风格的简单边框水印(水印1)，使用独立常量
            // 使用边框粗细设置计算基础边框宽度，与border_2percent保持一致
            let baseBorderWidth = actualBorderWidth
            
            // 使用左右边框粗细设置计算左右边框宽度
            // 额外的左右边框宽度为屏幕高度的0-10%，由leftRightBorderThicknessMultiplier控制
            let leftRightMultiplier = settings.leftRightBorderThicknessMultiplier
            // 基础边框宽度 + 额外的左右边框宽度
            let leftRightWidth = baseBorderWidth + UIScreen.main.bounds.height * 0.1 * CGFloat(leftRightMultiplier)
            
            return CustomWatermarkStyle18(
                borderWidth: leftRightWidth, // 使用计算后的左右边框宽度
                topBottomBorderWidth: baseBorderWidth, // 使用基础边框宽度作为上下边框宽度，不受topBottomBorderThicknessMultiplier影响
                borderColor: commonBorderColor
            )
            
        case "custom7":
            // 自定义水印7，基于胶片风格，但具有独立的常量
            let custom7FixedBorder = UIScreen.main.bounds.height * WatermarkConstants.Custom7.fixedBorderScreenHeightFactor
            return Custom7WatermarkStyle(borderWidth: custom7FixedBorder, borderColor: WatermarkConstants.Custom7.borderColor)
            
        case "custom4":
            // 自定义水印4，类似拍立得但具有不同的边框宽度和布局
            // 顶部和左右边框使用计算出的边框宽度
            let sideTopWidth = actualBorderWidth
            // 使用宽边框粗细设置计算底部边框宽度
            let bottomWidth: CGFloat = UIScreen.main.bounds.height * WideBorderThicknessUtils.mapSliderValueToActualThickness(
                sliderValue: settings.wideBorderThicknessMultiplier, 
                watermarkType: "custom4"
            )
            
            return CustomWatermarkStyle4(
                topBorderWidth: sideTopWidth, 
                bottomBorderWidth: bottomWidth,
                sideBorderWidth: sideTopWidth,
                borderColor: commonBorderColor
            )
            
        case "custom9":
            // 自定义水印9，基于水印4但新增署名功能
            // 顶部和左右边框使用计算出的边框宽度
            let sideTopWidth = actualBorderWidth
            // 使用宽边框粗细设置计算底部边框宽度（与custom4相同的范围）
            let bottomWidth: CGFloat = UIScreen.main.bounds.height * WideBorderThicknessUtils.mapSliderValueToActualThickness(
                sliderValue: settings.wideBorderThicknessMultiplier, 
                watermarkType: "custom9"
            )
            
            return CustomWatermarkStyle9(
                topBorderWidth: sideTopWidth, 
                bottomBorderWidth: bottomWidth,
                sideBorderWidth: sideTopWidth,
                borderColor: commonBorderColor
            )
            
        case "custom5":
            // 自定义水印5，三边边框宽度一致，一边宽边框（位置根据设置）
            let normalBorderWidth = actualBorderWidth
            // 使用宽边框粗细设置计算宽边框宽度
            let wideBorderWidth = UIScreen.main.bounds.height * WideBorderThicknessUtils.mapSliderValueToActualThickness(
                sliderValue: settings.wideBorderThicknessMultiplier, 
                watermarkType: "custom5"
            )
            
            // 根据设置决定宽边框位置
            if settings.isRightWideBorderEnabled {
                // 宽边框在右侧
            return CustomWatermarkStyle5(
                normalBorderWidth: normalBorderWidth,
                    wideBorderWidth: wideBorderWidth,
                    wideBorderPosition: .right,
                borderColor: commonBorderColor
            )
            } else {
                // 宽边框在左侧（默认）
                return CustomWatermarkStyle5(
                    normalBorderWidth: normalBorderWidth,
                    wideBorderWidth: wideBorderWidth,
                    wideBorderPosition: .left,
                    borderColor: commonBorderColor
                )
            }

        case "custom6":
            // 自定义水印6，上下宽边框风格
            // 水印6也需要使用统一边框颜色
            let sideWidth = actualBorderWidth // 左右边框使用计算出的标准边框宽度
            
            // 使用宽边框粗细设置控制上下边框
            let wideBorderMultiplier = CGFloat(settings.wideBorderThicknessMultiplier)
            
            // 检查是否启用等宽模式
            if settings.isEqualWidthBorderEnabled {
                // 等宽模式：上下边框厚度一致，粗细范围从6%-12%
                let minEqualWidth: CGFloat = UIScreen.main.bounds.height * 0.06 // 最小6%屏幕高度
                let maxEqualWidth: CGFloat = UIScreen.main.bounds.height * 0.12 // 最大12%屏幕高度
                
                // 线性映射：从wideBorderMultiplier (0-1) 映射到 6%-12%范围
                let equalBorderWidth = minEqualWidth + (maxEqualWidth - minEqualWidth) * wideBorderMultiplier
                
                return CustomWatermarkStyle6(
                    topBorderWidth: equalBorderWidth,
                    bottomBorderWidth: equalBorderWidth,
                    sideBorderWidth: sideWidth,
                    borderColor: commonBorderColor
                )
            } else {
                // 非等宽模式：保持原有的不对称设计
            // 上下边框使用同一宽边框粗细设置
            let topBaseWidth = UIScreen.main.bounds.height * WatermarkConstants.Custom6.baseTopBorderHeight
            let bottomBaseWidth = UIScreen.main.bounds.height * WatermarkConstants.Custom6.baseBottomBorderHeight
            
            // 应用线性增长：边框宽度增长50%
            let topWidthGrowth = topBaseWidth * 0.5 * wideBorderMultiplier
            let bottomWidthGrowth = bottomBaseWidth * 0.5 * wideBorderMultiplier
            
            let finalTopWidth = topBaseWidth + topWidthGrowth
            let finalBottomWidth = bottomBaseWidth + bottomWidthGrowth
            
            return CustomWatermarkStyle6(
                topBorderWidth: finalTopWidth,
                bottomBorderWidth: finalBottomWidth,
                sideBorderWidth: sideWidth,
                borderColor: commonBorderColor
            )
            }

        case "custom8":
            // 自定义水印8，基于胶片风格，署名替换Logo，支持中下位置
            let custom8FixedBorder = UIScreen.main.bounds.height * WatermarkConstants.Custom8.fixedBorderScreenHeightFactor
            return Custom8WatermarkStyle(borderWidth: custom8FixedBorder, borderColor: WatermarkConstants.Custom8.borderColor)
            
        case "custom10":
            // 自定义水印10，与水印5类似，但宽边框在右侧
            let normalBorderWidth = actualBorderWidth
            // 使用宽边框粗细设置计算右边框宽度
            let rightBorderWidth = UIScreen.main.bounds.height * WideBorderThicknessUtils.mapSliderValueToActualThickness(
                sliderValue: settings.wideBorderThicknessMultiplier, 
                watermarkType: "custom10"
            )
            
            return CustomWatermarkStyle10(
                normalBorderWidth: normalBorderWidth,
                wideBorderWidth: rightBorderWidth,
                borderColor: commonBorderColor
            )

        case "custom11":
            // 自定义水印11，与水印4完全相同，但独立设置
            // 顶部和左右边框使用计算出的边框宽度
            let sideTopWidth = actualBorderWidth
            // 使用宽边框粗细设置计算底部边框宽度
            let bottomWidth: CGFloat = UIScreen.main.bounds.height * WideBorderThicknessUtils.mapSliderValueToActualThickness(
                sliderValue: settings.wideBorderThicknessMultiplier, 
                watermarkType: "custom11"
            )
            
            return CustomWatermarkStyle11(
                topBorderWidth: sideTopWidth, 
                bottomBorderWidth: bottomWidth,
                sideBorderWidth: sideTopWidth,
                borderColor: commonBorderColor
            )
            
        case "custom12":
            // 自定义水印12，与水印11完全相同，但独立设置
            // 顶部和左右边框使用计算出的边框宽度
            let sideTopWidth = actualBorderWidth
            // 使用宽边框粗细设置计算底部边框宽度
            let bottomWidth: CGFloat = UIScreen.main.bounds.height * WideBorderThicknessUtils.mapSliderValueToActualThickness(
                sliderValue: settings.wideBorderThicknessMultiplier, 
                watermarkType: "custom12"
            )
            
            return CustomWatermarkStyle12(
                topBorderWidth: sideTopWidth, 
                bottomBorderWidth: bottomWidth,
                sideBorderWidth: sideTopWidth,
                borderColor: commonBorderColor
            )

        case "custom13":
            // 自定义水印13，与水印12完全相同，但独立设置
            // 顶部和左右边框使用计算出的边框宽度
            let sideTopWidth = actualBorderWidth
            // 使用宽边框粗细设置计算底部边框宽度
            let bottomWidth: CGFloat = UIScreen.main.bounds.height * WideBorderThicknessUtils.mapSliderValueToActualThickness(
                sliderValue: settings.wideBorderThicknessMultiplier, 
                watermarkType: "custom13"
            )
            
            return CustomWatermarkStyle13(
                topBorderWidth: sideTopWidth, 
                bottomBorderWidth: bottomWidth,
                sideBorderWidth: sideTopWidth,
                borderColor: commonBorderColor
            )
            
        case "custom14":
            // 自定义水印14，与水印13完全相同，但独立设置
            // 顶部和左右边框使用计算出的边框宽度
            let sideTopWidth = actualBorderWidth
            // 使用宽边框粗细设置计算底部边框宽度
            let bottomWidth: CGFloat = UIScreen.main.bounds.height * WideBorderThicknessUtils.mapSliderValueToActualThickness(
                sliderValue: settings.wideBorderThicknessMultiplier, 
                watermarkType: "custom14"
            )
            
            return CustomWatermarkStyle14(
                topBorderWidth: sideTopWidth, 
                bottomBorderWidth: bottomWidth,
                sideBorderWidth: sideTopWidth,
                borderColor: commonBorderColor
            )

        case "custom15":
            // 自定义水印15，基于自定义水印2（拍立得水印）
            // 拍立得的上/左/右边框使用计算出的边框宽度
            let sideTopWidth = actualBorderWidth
            // 使用宽边框粗细设置计算底部边框宽度
            let bottomWidth: CGFloat = UIScreen.main.bounds.height * WideBorderThicknessUtils.mapSliderValueToActualThickness(
                sliderValue: settings.wideBorderThicknessMultiplier, 
                watermarkType: "custom15"
            )
            
            return CustomWatermarkStyle15(
                topBorderWidth: sideTopWidth, 
                bottomBorderWidth: bottomWidth,
                sideBorderWidth: sideTopWidth,
                borderColor: commonBorderColor
            )
            
        case "custom16":
            // 自定义水印16，与水印10类似，上下左边框宽度一致，右边框宽度较宽
            let normalBorderWidth = actualBorderWidth
            // 使用宽边框粗细设置计算右边框宽度
            let rightBorderWidth = UIScreen.main.bounds.height * WideBorderThicknessUtils.mapSliderValueToActualThickness(
                sliderValue: settings.wideBorderThicknessMultiplier, 
                watermarkType: "custom16"
            )
            
            return CustomWatermarkStyle16(
                normalBorderWidth: normalBorderWidth,
                wideBorderWidth: rightBorderWidth,
                borderColor: commonBorderColor
            )

        case "custom19":
            // 自定义水印19，基于自定义水印15，用于大疆水印样式
            // 上/左/右边框使用计算出的边框宽度
            let sideTopWidth = actualBorderWidth
            // 使用宽边框粗细设置计算底部边框宽度
            let bottomWidth: CGFloat = UIScreen.main.bounds.height * WideBorderThicknessUtils.mapSliderValueToActualThickness(
                sliderValue: settings.wideBorderThicknessMultiplier, 
                watermarkType: "custom19"
            )
            
            return CustomWatermarkStyle19(
                topBorderWidth: sideTopWidth, 
                bottomBorderWidth: bottomWidth,
                sideBorderWidth: sideTopWidth,
                borderColor: commonBorderColor
            )

        case "custom20":
            // 自定义水印20，基于自定义水印19，完全独立复制
            // 上/左/右边框使用计算出的边框宽度
            let sideTopWidth = actualBorderWidth
            // 使用宽边框粗细设置计算底部边框宽度
            let bottomWidth: CGFloat = UIScreen.main.bounds.height * WideBorderThicknessUtils.mapSliderValueToActualThickness(
                sliderValue: settings.wideBorderThicknessMultiplier, 
                watermarkType: "custom20"
            )
            
            return CustomWatermarkStyle20(
                topBorderWidth: sideTopWidth, 
                bottomBorderWidth: bottomWidth,
                sideBorderWidth: sideTopWidth,
                borderColor: commonBorderColor
            )
            
        case "custom21":
            // 自定义水印21，基于自定义水印19，完全独立复制
            // 上/左/右边框使用计算出的边框宽度
            let sideTopWidth = actualBorderWidth
            // 使用宽边框粗细设置计算底部边框宽度
            let bottomWidth: CGFloat = UIScreen.main.bounds.height * WideBorderThicknessUtils.mapSliderValueToActualThickness(
                sliderValue: settings.wideBorderThicknessMultiplier, 
                watermarkType: "custom21"
            )
            
            return CustomWatermarkStyle21(
                topBorderWidth: sideTopWidth, 
                bottomBorderWidth: bottomWidth,
                sideBorderWidth: sideTopWidth,
                borderColor: commonBorderColor
            )

        case "custom22":
            // 自定义水印22，基于胶片风格(水印17)，但使用独立常量
            let custom22FixedBorder = UIScreen.main.bounds.height * WatermarkConstants.Custom22.fixedBorderScreenHeightFactor
            return CustomWatermarkStyle22(borderWidth: custom22FixedBorder, borderColor: WatermarkConstants.Custom22.borderColor)

        case "custom23":
            // 自定义水印23，基于自定义水印1，使用用户设置的边框宽度和颜色，符合统一调用原则
            return CustomWatermarkStyle23(borderWidth: actualBorderWidth, borderColor: commonBorderColor)

        case "custom24":
            // 自定义水印24，基于自定义水印23，使用用户设置的边框宽度和颜色，符合统一调用原则
            return CustomWatermarkStyle24(borderWidth: actualBorderWidth, borderColor: commonBorderColor)

        case "custom25":
            // 自定义水印25，基于自定义水印15，属于经典标题
            // 上/左/右边框使用计算出的边框宽度
            let sideTopWidth = actualBorderWidth
            // 使用宽边框粗细设置计算底部边框宽度
            let bottomWidth: CGFloat = UIScreen.main.bounds.height * WideBorderThicknessUtils.mapSliderValueToActualThickness(
                sliderValue: settings.wideBorderThicknessMultiplier, 
                watermarkType: "custom25"
            )
            
            return CustomWatermarkStyle25(
                topBorderWidth: sideTopWidth, 
                bottomBorderWidth: bottomWidth,
                sideBorderWidth: sideTopWidth,
                borderColor: commonBorderColor
            )

        case "none": // 代表不应用任何样式
            return nil
        default:
            // 对于自定义样式，如果它们也需要支持统一的粗细和颜色设置，
            // 可以在这里添加它们的case，并使用 actualBorderWidth 和 commonBorderColor。
            // 例如: if type.starts(with: "custom_style") { ... }
            print("⚠️ WatermarkStyleFactory: 未知的水印类型 '\(type)'或该类型尚不支持通用粗细/颜色设置。")
            return nil
        }
    }
}

// 偏好标签创建器
class PreferenceLabelCreator {
    static func createLabel(for option: String, with settings: WatermarkSettings, enabledElementsCount: Int = 3, customFontSizeFactors: (single: CGFloat, two: CGFloat, three: CGFloat)? = nil, fixedFontSize: CGFloat? = nil) -> UILabel {
        // 使用PreferenceUtils创建偏好标签
        return PreferenceUtils.createPreferenceLabel(
            for: option,
            with: settings,
            enabledElementsCount: enabledElementsCount,
            customFontSizeFactors: customFontSizeFactors,
            fixedFontSize: fixedFontSize
        )
    }
}

// Logo创建器
class LogoCreator {
    
    /// 创建指定名称的Logo视图
    static func createLogo(logoName: String, with settings: WatermarkSettings, enabledElementsCount: Int = 3, customSizeFactors: (single: CGFloat, two: CGFloat, three: CGFloat)? = nil, fixedSize: CGFloat? = nil) -> UIImageView {
        // 计算基础尺寸
        var baseSize: CGFloat
        if let size = fixedSize {
            baseSize = size
        } else if let factors = customSizeFactors {
            if enabledElementsCount == 1 {
                baseSize = UIScreen.main.bounds.height * factors.single
            } else if enabledElementsCount == 2 {
                baseSize = UIScreen.main.bounds.height * factors.two
            } else {
                baseSize = UIScreen.main.bounds.height * factors.three
            }
        } else {
            baseSize = UIScreen.main.bounds.height * 0.02
        }
        
        // 应用Logo大小乘数后的最终尺寸
        let finalSize = baseSize * CGFloat(settings.logoSizeMultiplier)
        
        // 使用LogoUtils创建Logo图像视图，传入调整后的固定尺寸
        return LogoUtils.createLogoImageView(
            logoName: logoName,
            with: settings,
            enabledElementsCount: enabledElementsCount,
            customSizeFactors: nil, // 不再使用自定义因子，因为已经计算好了finalSize
            fixedSize: finalSize
        )
    }
    
    static func createLogo(with settings: WatermarkSettings, enabledElementsCount: Int = 3, customSizeFactors: (single: CGFloat, two: CGFloat, three: CGFloat)? = nil, fixedSize: CGFloat? = nil) -> UIImageView {
        // 计算基础尺寸
        var baseSize: CGFloat
        if let size = fixedSize {
            baseSize = size
        } else if let factors = customSizeFactors {
            if enabledElementsCount == 1 {
                baseSize = UIScreen.main.bounds.height * factors.single
            } else if enabledElementsCount == 2 {
                baseSize = UIScreen.main.bounds.height * factors.two
            } else {
                baseSize = UIScreen.main.bounds.height * factors.three
            }
        } else {
            baseSize = UIScreen.main.bounds.height * 0.02
        }
        
        // 应用Logo大小乘数后的最终尺寸
        let finalSize = baseSize * CGFloat(settings.logoSizeMultiplier)
        
        // 使用LogoUtils创建Logo图像视图，传入调整后的固定尺寸
        return LogoUtils.createLogoImageView(
            with: settings,
            enabledElementsCount: enabledElementsCount,
            customSizeFactors: nil, // 不再使用自定义因子，因为已经计算好了finalSize
            fixedSize: finalSize
        )
    }
}

// 文字标签创建器
class TextLabelCreator {
    static func createLabel(with settings: WatermarkSettings, 
                           text: String? = nil,
                           enabledElementsCount: Int = 3, 
                           customFontSizeFactors: (single: CGFloat, two: CGFloat, three: CGFloat)? = nil, 
                           fixedFontSize: CGFloat? = nil) -> UILabel {
        
        // 使用TextUtils创建水印文字标签
        return TextUtils.createTextLabel(
            with: settings,
            text: text,
            enabledElementsCount: enabledElementsCount,
            customFontSizeFactors: customFontSizeFactors,
            fixedFontSize: fixedFontSize
        )
    }
}

// MARK: - 宽边框粗细工具类

// MARK: - 宽边框粗细工具类
/// 处理宽边框粗细的映射逻辑
struct WideBorderThicknessUtils {
    
    /// 将滑块值映射到实际的宽边框粗细值
    /// - Parameters:
    ///   - sliderValue: 滑块值 (0.0-1.0)
    ///   - watermarkType: 水印类型
    /// - Returns: 实际的屏幕高度百分比
    static func mapSliderValueToActualThickness(sliderValue: Double, watermarkType: String) -> CGFloat {
        switch watermarkType {
        case "polaroid": // 水印2
            // 范围：9%-18%，滑块0% → 9%，滑块100% → 18%
            let minValue: CGFloat = 0.09
            let maxValue: CGFloat = 0.18
            return minValue + CGFloat(sliderValue) * (maxValue - minValue)
            
        case "custom15": // 水印15 - 与水印2（拍立得）使用相同的范围
            // 范围：9%-18%，滑块0% → 9%，滑块100% → 18%
            let minValue: CGFloat = 0.09
            let maxValue: CGFloat = 0.18
            return minValue + CGFloat(sliderValue) * (maxValue - minValue)
            
        case "custom25": // 水印25 - 与水印15相同，使用相同的范围
            // 范围：9%-18%，滑块0% → 9%，滑块100% → 18%
            let minValue: CGFloat = 0.09
            let maxValue: CGFloat = 0.18
            return minValue + CGFloat(sliderValue) * (maxValue - minValue)
            
        case "custom19": // 水印19 - 与水印15相同，使用相同的范围
            // 范围：9%-18%，滑块0% → 9%，滑块100% → 18%
            let minValue: CGFloat = 0.09
            let maxValue: CGFloat = 0.18
            return minValue + CGFloat(sliderValue) * (maxValue - minValue)
            
        case "custom20": // 水印20 - 与水印19相同，使用相同的范围
            // 范围：9%-18%，滑块0% → 9%，滑块100% → 18%
            let minValue: CGFloat = 0.09
            let maxValue: CGFloat = 0.18
            return minValue + CGFloat(sliderValue) * (maxValue - minValue)
            
        case "custom21": // 水印21 - 与水印19相同，使用相同的范围
            // 范围：9%-18%，滑块0% → 9%，滑块100% → 18%
            let minValue: CGFloat = 0.09
            let maxValue: CGFloat = 0.18
            return minValue + CGFloat(sliderValue) * (maxValue - minValue)
            
        case "custom4": // 水印4
            // 范围：6%-9%，滑块0% → 6%，滑块100% → 9%
            let minValue: CGFloat = 0.06
            let maxValue: CGFloat = 0.09
            return minValue + CGFloat(sliderValue) * (maxValue - minValue)
            
        case "custom9": // 水印9 - 与水印4使用相同的范围
            // 范围：6%-9%，滑块0% → 6%，滑块100% → 9%
            let minValue: CGFloat = 0.06
            let maxValue: CGFloat = 0.09
            return minValue + CGFloat(sliderValue) * (maxValue - minValue)
            
        case "custom11": // 水印11 - 与水印4使用相同的范围
            // 范围：6%-9%，滑块0% → 6%，滑块100% → 9%
            let minValue: CGFloat = 0.06
            let maxValue: CGFloat = 0.09
            return minValue + CGFloat(sliderValue) * (maxValue - minValue)
            
        case "custom12": // 水印12 - 与水印11使用相同的范围
            // 范围：6%-9%，滑块0% → 6%，滑块100% → 9%
            let minValue: CGFloat = 0.06
            let maxValue: CGFloat = 0.09
            return minValue + CGFloat(sliderValue) * (maxValue - minValue)
            
        case "custom13": // 水印13 - 与水印12使用相同的范围
            // 范围：6%-9%，滑块0% → 6%，滑块100% → 9%
            let minValue: CGFloat = 0.06
            let maxValue: CGFloat = 0.09
            return minValue + CGFloat(sliderValue) * (maxValue - minValue)
            
        case "custom14": // 水印14 - 与水印13使用相同的范围
            // 范围：6%-9%，滑块0% → 6%，滑块100% → 9%
            let minValue: CGFloat = 0.06
            let maxValue: CGFloat = 0.09
            return minValue + CGFloat(sliderValue) * (maxValue - minValue)
            
        case "custom6": // 水印6
            // 在这里，我们只需要返回基础粗细值，因为边框的粗细和元素大小
            // 会在CustomWatermarkStyle6内部根据滑块值直接计算
            return CGFloat(sliderValue)
            
        case "custom5": // 水印5
            // 范围：20%-30%，滑块0% → 20%，滑块50% → 25%，滑块100% → 30%
            let minValue: CGFloat = 0.20
            let maxValue: CGFloat = 0.30
            return minValue + CGFloat(sliderValue) * (maxValue - minValue)
            
        case "custom10": // 水印10 - 与水印5使用相同的范围
            // 范围：20%-30%，滑块0% → 20%，滑块50% → 25%，滑块100% → 30%
            let minValue: CGFloat = 0.20
            let maxValue: CGFloat = 0.30
            return minValue + CGFloat(sliderValue) * (maxValue - minValue)
            
        case "custom16": // 水印16 - 与水印10使用相同的范围
            // 范围：20%-30%，滑块0% → 20%，滑块50% → 25%，滑块100% → 30%
            let minValue: CGFloat = 0.20
            let maxValue: CGFloat = 0.30
            return minValue + CGFloat(sliderValue) * (maxValue - minValue)
            
        default:
            // 默认情况，返回原始滑块值
            return CGFloat(sliderValue)
        }
    }
    
    /// 将实际粗细值映射回滑块值（用于初始化）
    /// - Parameters:
    ///   - actualThickness: 实际的屏幕高度百分比
    ///   - watermarkType: 水印类型
    /// - Returns: 滑块值 (0.0-1.0)
    static func mapActualThicknessToSliderValue(actualThickness: CGFloat, watermarkType: String) -> Double {
        switch watermarkType {
        case "polaroid": // 水印2
            let minValue: CGFloat = 0.09
            let maxValue: CGFloat = 0.18
            let sliderValue = (actualThickness - minValue) / (maxValue - minValue)
            return Double(max(0.0, min(1.0, sliderValue))) // 确保在0-1范围内
            
        case "custom15": // 水印15 - 与水印2（拍立得）使用相同的逻辑
            let minValue: CGFloat = 0.09
            let maxValue: CGFloat = 0.18
            let sliderValue = (actualThickness - minValue) / (maxValue - minValue)
            return Double(max(0.0, min(1.0, sliderValue))) // 确保在0-1范围内
            
        case "custom25": // 水印25 - 与水印15相同，使用相同的逻辑
            let minValue: CGFloat = 0.09
            let maxValue: CGFloat = 0.18
            let sliderValue = (actualThickness - minValue) / (maxValue - minValue)
            return Double(max(0.0, min(1.0, sliderValue))) // 确保在0-1范围内
            
        case "custom19": // 水印19 - 与水印15相同，使用相同的逻辑
            let minValue: CGFloat = 0.09
            let maxValue: CGFloat = 0.18
            let sliderValue = (actualThickness - minValue) / (maxValue - minValue)
            return Double(max(0.0, min(1.0, sliderValue))) // 确保在0-1范围内
            
        case "custom4": // 水印4
            let minValue: CGFloat = 0.06
            let maxValue: CGFloat = 0.09
            let sliderValue = (actualThickness - minValue) / (maxValue - minValue)
            return Double(max(0.0, min(1.0, sliderValue))) // 确保在0-1范围内
            
        case "custom9": // 水印9 - 与水印4使用相同的逻辑
            let minValue: CGFloat = 0.06
            let maxValue: CGFloat = 0.09
            let sliderValue = (actualThickness - minValue) / (maxValue - minValue)
            return Double(max(0.0, min(1.0, sliderValue))) // 确保在0-1范围内
            
        case "custom11": // 水印11 - 与水印4使用相同的逻辑
            let minValue: CGFloat = 0.06
            let maxValue: CGFloat = 0.09
            let sliderValue = (actualThickness - minValue) / (maxValue - minValue)
            return Double(max(0.0, min(1.0, sliderValue))) // 确保在0-1范围内
            
        case "custom12": // 水印12 - 与水印11使用相同的逻辑
            let minValue: CGFloat = 0.06
            let maxValue: CGFloat = 0.09
            let sliderValue = (actualThickness - minValue) / (maxValue - minValue)
            return Double(max(0.0, min(1.0, sliderValue))) // 确保在0-1范围内
            
        case "custom13": // 水印13 - 与水印12使用相同的逻辑
            let minValue: CGFloat = 0.06
            let maxValue: CGFloat = 0.09
            let sliderValue = (actualThickness - minValue) / (maxValue - minValue)
            return Double(max(0.0, min(1.0, sliderValue))) // 确保在0-1范围内
            
        case "custom14": // 水印14 - 与水印13使用相同的逻辑
            let minValue: CGFloat = 0.06
            let maxValue: CGFloat = 0.09
            let sliderValue = (actualThickness - minValue) / (maxValue - minValue)
            return Double(max(0.0, min(1.0, sliderValue))) // 确保在0-1范围内
            
        case "custom6": // 水印6
            // 返回原始滑块值，因为我们直接使用滑块值作为调整比例
            return Double(actualThickness)
            
        case "custom5": // 水印5
            let minValue: CGFloat = 0.20
            let maxValue: CGFloat = 0.30
            let sliderValue = (actualThickness - minValue) / (maxValue - minValue)
            return Double(max(0.0, min(1.0, sliderValue))) // 确保在0-1范围内
            
        case "custom10": // 水印10 - 与水印5使用相同的逻辑
            let minValue: CGFloat = 0.20
            let maxValue: CGFloat = 0.30
            let sliderValue = (actualThickness - minValue) / (maxValue - minValue)
            return Double(max(0.0, min(1.0, sliderValue))) // 确保在0-1范围内
            
        case "custom16": // 水印16 - 与水印10使用相同的逻辑
            let minValue: CGFloat = 0.20
            let maxValue: CGFloat = 0.30
            let sliderValue = (actualThickness - minValue) / (maxValue - minValue)
            return Double(max(0.0, min(1.0, sliderValue))) // 确保在0-1范围内
            
        default:
            return Double(actualThickness)
        }
    }
    
    /// 获取初始滑块值
    /// - Parameter watermarkType: 水印类型
    /// - Returns: 对应的默认滑块值
    static func getDefaultSliderValue(for watermarkType: String) -> Double {
        switch watermarkType {
        case "polaroid": // 水印2 - 初始值9%对应滑块0%
            return 0.0
        case "custom15": // 水印15 - 初始值9%对应滑块0%，与水印2相同
            return 0.0
        case "custom19": // 水印19 - 初始值9%对应滑块0%，与水印15相同
            return 0.0
        case "custom25": // 水印25 - 初始值9%对应滑块0%，与水印15相同
            return 0.0
        case "custom4": // 水印4 - 初始值6%对应滑块0%
            return 0.0
        case "custom9": // 水印9 - 初始值6%对应滑块0%
            return 0.0
        case "custom11": // 水印11 - 初始值6%对应滑块0%
            return 0.0
        case "custom12": // 水印12 - 初始值6%对应滑块0%，与水印11相同
            return 0.0
        case "custom13": // 水印13 - 初始值6%对应滑块0%，与水印12相同
            return 0.0
        case "custom14": // 水印14 - 初始值6%对应滑块0%，与水印13相同
            return 0.0
        case "custom5": // 水印5 - 初始值25%对应滑块50%
            return 0.5
        case "custom10": // 水印10 - 初始值25%对应滑块50%，与水印5相同
            return 0.5
        case "custom16": // 水印16 - 初始值25%对应滑块50%，与水印10相同
            return 0.5
        case "custom6": // 水印6 - 初始值设为滑块0%
            return 0.0
        default:
            return 0.0
        }
    }
}

// MARK: - 边框样式工具类