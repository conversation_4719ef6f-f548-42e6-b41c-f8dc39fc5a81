#!/bin/bash
# Copyright (c) 2025 LoniceraLab. All rights reserved.

# 修复水印模块重复文件编译错误脚本

echo "🔧 修复水印模块重复文件编译错误..."
echo "📍 项目目录: /Users/<USER>/Lomo/Lomo"
echo ""

PROJECT_DIR="/Users/<USER>/Lomo/Lomo"

# 创建修复备份目录
BACKUP_DIR="$PROJECT_DIR/Scripts/duplicate_files_backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

echo "📦 创建备份目录: $BACKUP_DIR"
echo ""

echo "1️⃣ 处理备份文件问题..."

# 移除或重命名备份文件，避免被 Xcode 编译
backup_files=(
    "Views/Edit/Components/WatermarkControlView.swift.backup"
    "Views/Edit/Components/WatermarkControlView.swift.duplicate_backup"
    "Views/Edit/Components/WatermarkControlView.swift.textinput_backup"
)

for backup_file in "${backup_files[@]}"; do
    file_path="$PROJECT_DIR/$backup_file"
    if [ -f "$file_path" ]; then
        # 备份到我们的备份目录
        cp "$file_path" "$BACKUP_DIR/"
        echo "📦 备份 $backup_file"
        
        # 重命名文件，添加 .bak 扩展名避免编译
        mv "$file_path" "$file_path.bak"
        echo "🔄 重命名 $backup_file -> $backup_file.bak"
    else
        echo "⚠️ $backup_file 不存在"
    fi
done

echo ""
echo "2️⃣ 检查重复的水印样式文件..."

# 检查是否有重复的水印样式文件
echo "📁 检查 Managers/Edit/WatermarkStyles/ 目录..."
if [ -d "$PROJECT_DIR/Managers/Edit/WatermarkStyles" ]; then
    style_files=$(find "$PROJECT_DIR/Managers/Edit/WatermarkStyles" -name "CustomWatermarkStyle*.swift" | wc -l)
    echo "✅ 发现 $style_files 个样式文件"
    
    # 检查是否有重复文件名
    duplicates=$(find "$PROJECT_DIR" -name "CustomWatermarkStyle*.swift" | sort | uniq -d)
    if [ -n "$duplicates" ]; then
        echo "⚠️ 发现重复的样式文件:"
        echo "$duplicates"
    else
        echo "✅ 没有发现重复的样式文件"
    fi
else
    echo "❌ WatermarkStyles 目录不存在"
fi

echo ""
echo "3️⃣ 检查备份目录中的重复文件..."

# 检查 Scripts 目录中的备份文件是否被意外包含
backup_scripts_files=$(find "$PROJECT_DIR/Scripts" -name "*Watermark*.swift" 2>/dev/null | wc -l)
if [ "$backup_scripts_files" -gt 0 ]; then
    echo "⚠️ Scripts 目录中发现 $backup_scripts_files 个水印相关文件:"
    find "$PROJECT_DIR/Scripts" -name "*Watermark*.swift" 2>/dev/null
    
    echo "🔄 移动这些文件到备份目录..."
    find "$PROJECT_DIR/Scripts" -name "*Watermark*.swift" -exec mv {} "$BACKUP_DIR/" \;
    echo "✅ 已移动到备份目录"
else
    echo "✅ Scripts 目录中没有水印相关文件"
fi

echo ""
echo "4️⃣ 清理 Xcode 缓存..."

echo "🧹 清理 Xcode DerivedData..."
# 使用我们的智能清理脚本
if [ -f "$PROJECT_DIR/Scripts/smart_xcode_cache_cleaner.sh" ]; then
    "$PROJECT_DIR/Scripts/smart_xcode_cache_cleaner.sh"
else
    # 手动清理
    rm -rf ~/Library/Developer/Xcode/DerivedData/Lomo-*
    echo "✅ 手动清理 DerivedData"
fi

echo ""
echo "5️⃣ 验证文件结构..."

echo "📋 当前水印文件结构:"
echo "Manager 文件:"
find "$PROJECT_DIR/Managers/Edit" -name "*Watermark*" -type f | head -5
echo "..."

echo ""
echo "样式文件:"
style_count=$(find "$PROJECT_DIR/Managers/Edit/WatermarkStyles" -name "CustomWatermarkStyle*.swift" 2>/dev/null | wc -l)
echo "✅ $style_count 个样式文件"

echo ""
echo "视图文件:"
view_files=$(find "$PROJECT_DIR/Views/Edit/Components" -name "WatermarkControlView.swift" 2>/dev/null | wc -l)
echo "✅ $view_files 个控制视图文件"

echo ""
echo "备份文件 (.bak):"
bak_files=$(find "$PROJECT_DIR/Views/Edit/Components" -name "*.swift.*.bak" 2>/dev/null | wc -l)
echo "✅ $bak_files 个备份文件 (已重命名，不会被编译)"

echo ""
echo "6️⃣ 检查 Xcode 项目文件引用..."

# 检查 .xcodeproj 文件中是否有对备份文件的引用
if [ -f "$PROJECT_DIR/../Lomo.xcodeproj/project.pbxproj" ]; then
    echo "🔍 检查项目文件中的引用..."
    
    # 检查是否有对备份文件的引用
    backup_refs=$(grep -c "\.backup\|\.duplicate_backup\|\.textinput_backup" "$PROJECT_DIR/../Lomo.xcodeproj/project.pbxproj" 2>/dev/null || echo "0")
    if [ "$backup_refs" -gt 0 ]; then
        echo "⚠️ 项目文件中发现 $backup_refs 个备份文件引用"
        echo "💡 建议在 Xcode 中手动移除这些引用"
    else
        echo "✅ 项目文件中没有备份文件引用"
    fi
    
    # 检查重复的样式文件引用
    style_refs=$(grep -c "CustomWatermarkStyle" "$PROJECT_DIR/../Lomo.xcodeproj/project.pbxproj" 2>/dev/null || echo "0")
    echo "📊 项目文件中有 $style_refs 个样式文件引用"
else
    echo "⚠️ 无法找到 Xcode 项目文件"
fi

echo ""
echo "🎯 修复建议:"
echo "1. 重新打开 Xcode 项目"
echo "2. 在 Project Navigator 中检查是否有红色的文件引用"
echo "3. 删除任何对 .backup 文件的引用"
echo "4. 确保每个 CustomWatermarkStyle 文件只被引用一次"
echo "5. 重新编译项目"

echo ""
echo "🔧 如果问题仍然存在:"
echo "1. 在 Xcode 中选择 Product -> Clean Build Folder"
echo "2. 重启 Xcode"
echo "3. 检查 Build Phases -> Compile Sources 中是否有重复文件"

echo ""
echo "✅ 修复完成！"
echo "📦 备份位置: $BACKUP_DIR"
echo "🔄 备份文件已重命名为 .bak 扩展名"