#!/bin/bash
# Copyright (c) 2025 LoniceraLab. All rights reserved.

# 简单直接修复水印编译错误

echo "🔧 直接修复水印编译错误..."

PROJECT_DIR="/Users/<USER>/Lomo/Lomo"

# 1. 删除所有备份文件
echo "1️⃣ 删除备份文件..."
find "$PROJECT_DIR" -name "*.swift.backup*" -delete 2>/dev/null
find "$PROJECT_DIR" -name "*.swift.duplicate*" -delete 2>/dev/null
find "$PROJECT_DIR" -name "*.swift.textinput*" -delete 2>/dev/null
find "$PROJECT_DIR" -name "*.swift.bak" -delete 2>/dev/null
echo "✅ 备份文件已删除"

# 2. 删除Scripts目录中的所有备份目录
echo "2️⃣ 清理Scripts备份目录..."
rm -rf "$PROJECT_DIR/Scripts/watermark_current_backup_"*
rm -rf "$PROJECT_DIR/Scripts/mvvm_components_backup_"*
rm -rf "$PROJECT_DIR/Scripts/duplicate_files_backup_"*
rm -rf "$PROJECT_DIR/Scripts/final_backup_"*
rm -rf "$PROJECT_DIR/Scripts/cleanup_backup_"*
echo "✅ Scripts备份目录已清理"

# 3. 删除Managers/Edit/目录下的重复样式文件（保留WatermarkStyles目录中的）
echo "3️⃣ 删除重复样式文件..."
find "$PROJECT_DIR/Managers/Edit" -maxdepth 1 -name "CustomWatermarkStyle*.swift" -delete 2>/dev/null
echo "✅ 重复样式文件已删除"

# 4. 清理Xcode缓存
echo "4️⃣ 清理Xcode缓存..."
rm -rf ~/Library/Developer/Xcode/DerivedData/Lomo-*
echo "✅ Xcode缓存已清理"

echo ""
echo "🎉 修复完成！现在重新打开Xcode编译项目"