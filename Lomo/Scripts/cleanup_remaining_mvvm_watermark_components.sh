#!/bin/bash
# Copyright (c) 2025 LoniceraLab. All rights reserved.

# 清理剩余的 MVVM-S 水印组件脚本

echo "🧹 清理剩余的 MVVM-S 水印组件..."
echo "📍 项目目录: /Users/<USER>/Lomo/Lomo"
echo ""

PROJECT_DIR="/Users/<USER>/Lomo/Lomo"

# 创建备份目录
BACKUP_DIR="$PROJECT_DIR/Scripts/mvvm_components_backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

echo "📦 备份剩余的 MVVM-S 组件到: $BACKUP_DIR"

# 需要清理的 MVVM-S 组件
components=(
    "WatermarkStyleGridView.swift"
    "WatermarkCategoryBarView.swift"
    "WatermarkOptionsPanelView.swift"
)

echo "1️⃣ 备份并删除 MVVM-S 组件..."

for component in "${components[@]}"; do
    file_path="$PROJECT_DIR/Views/Edit/Components/$component"
    if [ -f "$file_path" ]; then
        # 备份文件
        cp "$file_path" "$BACKUP_DIR/"
        echo "📦 备份 $component"
        
        # 删除文件
        rm "$file_path"
        echo "🗑️ 删除 $component"
    else
        echo "⚠️ $component 不存在"
    fi
done

echo ""
echo "2️⃣ 验证清理结果..."

# 检查是否还有其他 MVVM-S 水印组件
remaining_components=$(find "$PROJECT_DIR/Views/Edit/Components" -name "Watermark*View.swift" -not -name "WatermarkControlView.swift" 2>/dev/null | wc -l)

if [ "$remaining_components" -eq 0 ]; then
    echo "✅ 所有 MVVM-S 水印组件已清理完成"
else
    echo "⚠️ 仍有 $remaining_components 个 MVVM-S 水印组件:"
    find "$PROJECT_DIR/Views/Edit/Components" -name "Watermark*View.swift" -not -name "WatermarkControlView.swift" 2>/dev/null
fi

echo ""
echo "3️⃣ 检查保留的文件..."

if [ -f "$PROJECT_DIR/Views/Edit/Components/WatermarkControlView.swift" ]; then
    echo "✅ WatermarkControlView.swift 保留 (Lomo(76) 版本)"
else
    echo "❌ WatermarkControlView.swift 丢失"
fi

echo ""
echo "🎉 MVVM-S 组件清理完成！"
echo "📦 备份位置: $BACKUP_DIR"
echo "📋 现在只保留 Lomo(76) 版本的 WatermarkControlView.swift"