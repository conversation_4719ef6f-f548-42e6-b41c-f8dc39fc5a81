#!/bin/bash
# Copyright (c) 2025 LoniceraLab. All rights reserved.

# 彻底清理重复的水印文件

echo "🗑️ 彻底清理重复的水印文件..."
echo "📍 项目目录: /Users/<USER>/Lomo/Lomo"
echo ""

PROJECT_DIR="/Users/<USER>/Lomo/Lomo"

# 创建清理备份
CLEANUP_BACKUP_DIR="$PROJECT_DIR/Scripts/cleanup_backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$CLEANUP_BACKUP_DIR"

echo "📦 创建清理备份目录: $CLEANUP_BACKUP_DIR"
echo ""

echo "1️⃣ 识别并清理重复的样式文件..."

# 保留 Managers/Edit/WatermarkStyles/ 目录中的文件，删除其他位置的重复文件
CORRECT_STYLES_DIR="$PROJECT_DIR/Managers/Edit/WatermarkStyles"

if [ -d "$CORRECT_STYLES_DIR" ]; then
    echo "✅ 正确的样式目录存在: $CORRECT_STYLES_DIR"
    
    # 查找所有 CustomWatermarkStyle 文件
    for i in {1..25}; do
        style_file="CustomWatermarkStyle$i.swift"
        
        # 查找所有该文件的位置
        all_locations=$(find "$PROJECT_DIR" -name "$style_file" -type f)
        correct_location="$CORRECT_STYLES_DIR/$style_file"
        
        # 如果正确位置的文件存在
        if [ -f "$correct_location" ]; then
            echo "📋 处理 $style_file:"
            echo "   ✅ 保留: $correct_location"
            
            # 删除其他位置的重复文件
            while IFS= read -r file_path; do
                if [ "$file_path" != "$correct_location" ]; then
                    echo "   🗑️ 删除重复: $file_path"
                    # 备份后删除
                    cp "$file_path" "$CLEANUP_BACKUP_DIR/"
                    rm "$file_path"
                fi
            done <<< "$all_locations"
        else
            echo "⚠️ $style_file 在正确位置不存在"
        fi
    done
else
    echo "❌ 样式目录不存在: $CORRECT_STYLES_DIR"
fi

echo ""
echo "2️⃣ 清理重复的 WatermarkControlView.swift..."

# 保留 Views/Edit/Components/ 中的文件
CORRECT_CONTROL_VIEW="$PROJECT_DIR/Views/Edit/Components/WatermarkControlView.swift"

if [ -f "$CORRECT_CONTROL_VIEW" ]; then
    echo "✅ 正确的控制视图存在: $CORRECT_CONTROL_VIEW"
    
    # 查找所有 WatermarkControlView.swift 文件
    all_control_views=$(find "$PROJECT_DIR" -name "WatermarkControlView.swift" -type f)
    
    echo "📋 处理 WatermarkControlView.swift:"
    echo "   ✅ 保留: $CORRECT_CONTROL_VIEW"
    
    # 删除其他位置的重复文件
    while IFS= read -r file_path; do
        if [ "$file_path" != "$CORRECT_CONTROL_VIEW" ]; then
            echo "   🗑️ 删除重复: $file_path"
            # 备份后删除
            cp "$file_path" "$CLEANUP_BACKUP_DIR/"
            rm "$file_path"
        fi
    done <<< "$all_control_views"
else
    echo "❌ 控制视图在正确位置不存在"
fi

echo ""
echo "3️⃣ 清理备份目录中的文件..."

# 清理 Scripts 目录中的备份文件，避免被意外包含
backup_dirs=(
    "watermark_current_backup_*"
    "mvvm_components_backup_*"
    "duplicate_files_backup_*"
    "final_backup_*"
)

for backup_pattern in "${backup_dirs[@]}"; do
    backup_paths=$(find "$PROJECT_DIR/Scripts" -name "$backup_pattern" -type d 2>/dev/null)
    if [ -n "$backup_paths" ]; then
        echo "🧹 清理备份目录: $backup_pattern"
        while IFS= read -r backup_dir; do
            if [ -d "$backup_dir" ]; then
                # 移动到最终备份位置
                mv "$backup_dir" "$CLEANUP_BACKUP_DIR/"
                echo "   📦 移动: $backup_dir"
            fi
        done <<< "$backup_paths"
    fi
done

echo ""
echo "4️⃣ 清理其他可能的重复文件..."

# 清理可能存在的其他水印相关重复文件
other_watermark_files=(
    "WatermarkSettingsManager.swift"
    "WatermarkStyleManager.swift"
    "WatermarkStyles.swift"
    "WatermarkSettings.swift"
    "WatermarkConstants.swift"
)

for file in "${other_watermark_files[@]}"; do
    # 查找所有该文件的位置
    all_locations=$(find "$PROJECT_DIR" -name "$file" -type f)
    location_count=$(echo "$all_locations" | wc -l)
    
    if [ "$location_count" -gt 1 ]; then
        echo "⚠️ 发现 $location_count 个 $file 文件:"
        echo "$all_locations"
        echo "   请手动检查并保留正确的版本"
    elif [ "$location_count" -eq 1 ]; then
        echo "✅ $file: 1个文件，无重复"
    else
        echo "❌ $file: 未找到"
    fi
done

echo ""
echo "5️⃣ 最终验证..."

echo "📊 清理后的文件统计:"

# 统计样式文件
style_count=$(find "$PROJECT_DIR/Managers/Edit/WatermarkStyles" -name "CustomWatermarkStyle*.swift" 2>/dev/null | wc -l)
echo "✅ 样式文件: $style_count 个"

# 统计控制视图文件
control_view_count=$(find "$PROJECT_DIR" -name "WatermarkControlView.swift" -type f | wc -l)
echo "✅ 控制视图文件: $control_view_count 个"

# 统计总的水印文件
total_watermark_count=$(find "$PROJECT_DIR" -name "*Watermark*.swift" -type f | wc -l)
echo "✅ 总水印文件: $total_watermark_count 个"

echo ""
echo "6️⃣ 再次清理 Xcode 缓存..."

# 再次清理 Xcode 缓存
rm -rf ~/Library/Developer/Xcode/DerivedData/Lomo-*
echo "✅ 再次清理 DerivedData"

echo ""
echo "🎉 重复文件清理完成！"
echo ""
echo "📋 清理总结:"
echo "✅ 保留了正确位置的水印文件"
echo "✅ 删除了所有重复文件"
echo "✅ 清理了备份目录"
echo "✅ 再次清理了 Xcode 缓存"
echo ""
echo "📦 所有清理的文件备份在: $CLEANUP_BACKUP_DIR"
echo ""
echo "🔄 现在请:"
echo "1. 重新打开 Xcode 项目"
echo "2. 执行 Product -> Clean Build Folder"
echo "3. 重新编译项目"
echo "4. 编译应该成功，不再有重复文件错误"