#!/bin/bash
# Copyright (c) 2025 LoniceraLab. All rights reserved.

# 检查 Xcode 项目文件中的重复引用

echo "🔍 检查 Xcode 项目文件中的重复引用..."
echo "📍 项目目录: /Users/<USER>/Lomo"
echo ""

PROJECT_ROOT="/Users/<USER>/Lomo"
PBXPROJ_FILE="$PROJECT_ROOT/Lomo.xcodeproj/project.pbxproj"

if [ ! -f "$PBXPROJ_FILE" ]; then
    echo "❌ 无法找到 Xcode 项目文件: $PBXPROJ_FILE"
    exit 1
fi

echo "✅ 找到项目文件: $PBXPROJ_FILE"
echo ""

echo "1️⃣ 检查水印相关文件引用..."

# 检查所有水印相关文件的引用
echo "📋 水印文件引用统计:"
watermark_files=(
    "WatermarkControlView.swift"
    "WatermarkSettingsManager.swift"
    "WatermarkStyleManager.swift"
    "WatermarkStyles.swift"
    "WatermarkSettings.swift"
    "WatermarkConstants.swift"
)

for file in "${watermark_files[@]}"; do
    count=$(grep -c "$file" "$PBXPROJ_FILE" 2>/dev/null || echo "0")
    if [ "$count" -gt 2 ]; then
        echo "⚠️ $file: $count 次引用 (可能重复)"
    elif [ "$count" -eq 0 ]; then
        echo "❌ $file: $count 次引用 (未找到)"
    else
        echo "✅ $file: $count 次引用"
    fi
done

echo ""
echo "2️⃣ 检查备份文件引用..."

backup_patterns=(
    "\.backup"
    "\.duplicate_backup"
    "\.textinput_backup"
    "\.bak"
)

for pattern in "${backup_patterns[@]}"; do
    count=$(grep -c "$pattern" "$PBXPROJ_FILE" 2>/dev/null || echo "0")
    if [ "$count" -gt 0 ]; then
        echo "⚠️ 发现 $count 个 $pattern 文件引用"
        echo "   具体文件:"
        grep "$pattern" "$PBXPROJ_FILE" | head -3
        echo ""
    else
        echo "✅ 没有 $pattern 文件引用"
    fi
done

echo ""
echo "3️⃣ 检查自定义水印样式文件..."

echo "📊 CustomWatermarkStyle 文件引用统计:"
for i in {1..25}; do
    file="CustomWatermarkStyle$i.swift"
    count=$(grep -c "$file" "$PBXPROJ_FILE" 2>/dev/null || echo "0")
    if [ "$count" -gt 2 ]; then
        echo "⚠️ $file: $count 次引用 (可能重复)"
    elif [ "$count" -eq 0 ]; then
        echo "❌ $file: $count 次引用 (未找到)"
    fi
done

echo ""
echo "4️⃣ 检查重复的文件路径..."

echo "🔍 查找可能的重复路径:"
# 查找可能重复的文件路径
duplicates=$(grep -o '[^/]*Watermark[^/]*\.swift' "$PBXPROJ_FILE" | sort | uniq -d)
if [ -n "$duplicates" ]; then
    echo "⚠️ 发现重复的文件名:"
    echo "$duplicates"
else
    echo "✅ 没有发现重复的文件名"
fi

echo ""
echo "5️⃣ 生成清理建议..."

# 创建清理建议文件
SUGGESTIONS_FILE="$PROJECT_ROOT/Lomo/Scripts/xcode_cleanup_suggestions.txt"
echo "# Xcode 项目文件清理建议" > "$SUGGESTIONS_FILE"
echo "生成时间: $(date)" >> "$SUGGESTIONS_FILE"
echo "" >> "$SUGGESTIONS_FILE"

echo "## 需要在 Xcode 中手动处理的问题:" >> "$SUGGESTIONS_FILE"

# 检查备份文件引用
for pattern in "${backup_patterns[@]}"; do
    if grep -q "$pattern" "$PBXPROJ_FILE" 2>/dev/null; then
        echo "- 删除所有包含 '$pattern' 的文件引用" >> "$SUGGESTIONS_FILE"
    fi
done

# 检查重复引用
for file in "${watermark_files[@]}"; do
    count=$(grep -c "$file" "$PBXPROJ_FILE" 2>/dev/null || echo "0")
    if [ "$count" -gt 2 ]; then
        echo "- 检查 $file 的重复引用 ($count 次)" >> "$SUGGESTIONS_FILE"
    fi
done

echo "" >> "$SUGGESTIONS_FILE"
echo "## 操作步骤:" >> "$SUGGESTIONS_FILE"
echo "1. 在 Xcode 中打开项目" >> "$SUGGESTIONS_FILE"
echo "2. 在 Project Navigator 中查找红色的文件引用" >> "$SUGGESTIONS_FILE"
echo "3. 右键点击红色文件 -> Delete -> Move to Trash" >> "$SUGGESTIONS_FILE"
echo "4. 检查 Build Phases -> Compile Sources 中是否有重复文件" >> "$SUGGESTIONS_FILE"
echo "5. 删除重复的编译源文件" >> "$SUGGESTIONS_FILE"
echo "6. Clean Build Folder (Product -> Clean Build Folder)" >> "$SUGGESTIONS_FILE"
echo "7. 重新编译项目" >> "$SUGGESTIONS_FILE"

echo "📝 清理建议已保存到: $SUGGESTIONS_FILE"

echo ""
echo "6️⃣ 快速修复脚本..."

# 创建快速修复脚本（仅处理明显的问题）
QUICK_FIX_SCRIPT="$PROJECT_ROOT/Lomo/Scripts/quick_fix_project_file.sh"
cat > "$QUICK_FIX_SCRIPT" << 'EOF'
#!/bin/bash
# Copyright (c) 2025 LoniceraLab. All rights reserved.

# 快速修复 Xcode 项目文件中的备份文件引用

PROJECT_ROOT="/Users/<USER>/Lomo"
PBXPROJ_FILE="$PROJECT_ROOT/Lomo.xcodeproj/project.pbxproj"

if [ ! -f "$PBXPROJ_FILE" ]; then
    echo "❌ 无法找到项目文件"
    exit 1
fi

echo "🔧 快速修复项目文件..."

# 备份项目文件
cp "$PBXPROJ_FILE" "$PBXPROJ_FILE.backup_$(date +%Y%m%d_%H%M%S)"
echo "📦 已备份项目文件"

# 移除备份文件引用
sed -i '' '/\.backup/d' "$PBXPROJ_FILE"
sed -i '' '/\.duplicate_backup/d' "$PBXPROJ_FILE"
sed -i '' '/\.textinput_backup/d' "$PBXPROJ_FILE"

echo "✅ 已移除备份文件引用"
echo "🔄 请重新打开 Xcode 项目"
EOF

chmod +x "$QUICK_FIX_SCRIPT"
echo "🚀 快速修复脚本已创建: $QUICK_FIX_SCRIPT"

echo ""
echo "🎯 总结:"
echo "1. 主要问题是备份文件被包含在项目中"
echo "2. 需要在 Xcode 中手动移除这些引用"
echo "3. 或者运行快速修复脚本: $QUICK_FIX_SCRIPT"
echo "4. 详细建议请查看: $SUGGESTIONS_FILE"