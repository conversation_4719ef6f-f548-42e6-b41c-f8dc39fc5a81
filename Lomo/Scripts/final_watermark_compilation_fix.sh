#!/bin/bash
# Copyright (c) 2025 LoniceraLab. All rights reserved.

# 最终修复水印模块编译错误脚本

echo "🔧 最终修复水印模块编译错误..."
echo "📍 项目目录: /Users/<USER>/Lomo"
echo ""

PROJECT_ROOT="/Users/<USER>/Lomo"
PROJECT_DIR="$PROJECT_ROOT/Lomo"

echo "1️⃣ 清理所有备份文件..."

# 移动所有备份文件到安全位置
FINAL_BACKUP_DIR="$PROJECT_DIR/Scripts/final_backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$FINAL_BACKUP_DIR"

echo "📦 创建最终备份目录: $FINAL_BACKUP_DIR"

# 查找并移动所有可能导致编译冲突的文件
find "$PROJECT_DIR" -name "*.swift.backup*" -exec mv {} "$FINAL_BACKUP_DIR/" \; 2>/dev/null
find "$PROJECT_DIR" -name "*.swift.duplicate*" -exec mv {} "$FINAL_BACKUP_DIR/" \; 2>/dev/null
find "$PROJECT_DIR" -name "*.swift.textinput*" -exec mv {} "$FINAL_BACKUP_DIR/" \; 2>/dev/null
find "$PROJECT_DIR" -name "*.swift.bak" -exec mv {} "$FINAL_BACKUP_DIR/" \; 2>/dev/null

echo "✅ 已移动所有备份文件到安全位置"

echo ""
echo "2️⃣ 验证水印文件结构..."

# 确保只有正确的水印文件存在
echo "📋 当前水印文件结构:"

echo "Manager 文件:"
if [ -f "$PROJECT_DIR/Managers/Edit/WatermarkSettingsManager.swift" ]; then
    echo "✅ WatermarkSettingsManager.swift"
else
    echo "❌ WatermarkSettingsManager.swift 缺失"
fi

if [ -f "$PROJECT_DIR/Managers/Edit/WatermarkStyleManager.swift" ]; then
    echo "✅ WatermarkStyleManager.swift"
else
    echo "❌ WatermarkStyleManager.swift 缺失"
fi

if [ -f "$PROJECT_DIR/Managers/Edit/WatermarkStyles.swift" ]; then
    echo "✅ WatermarkStyles.swift"
else
    echo "❌ WatermarkStyles.swift 缺失"
fi

echo ""
echo "样式文件:"
style_count=$(find "$PROJECT_DIR/Managers/Edit/WatermarkStyles" -name "CustomWatermarkStyle*.swift" 2>/dev/null | wc -l)
echo "✅ $style_count 个自定义样式文件"

echo ""
echo "视图文件:"
if [ -f "$PROJECT_DIR/Views/Edit/Components/WatermarkControlView.swift" ]; then
    echo "✅ WatermarkControlView.swift"
else
    echo "❌ WatermarkControlView.swift 缺失"
fi

echo ""
echo "模型和常量:"
if [ -f "$PROJECT_DIR/Models/Edit/WatermarkSettings.swift" ]; then
    echo "✅ WatermarkSettings.swift"
else
    echo "❌ WatermarkSettings.swift 缺失"
fi

if [ -f "$PROJECT_DIR/Utils/Constants/WatermarkConstants.swift" ]; then
    echo "✅ WatermarkConstants.swift"
else
    echo "❌ WatermarkConstants.swift 缺失"
fi

echo ""
echo "3️⃣ 检查文件重复..."

# 检查是否有重复的文件
echo "🔍 检查重复文件:"
duplicates_found=false

# 检查 WatermarkControlView.swift 是否有多个版本
watermark_control_files=$(find "$PROJECT_DIR" -name "WatermarkControlView.swift" -type f | wc -l)
if [ "$watermark_control_files" -gt 1 ]; then
    echo "⚠️ 发现 $watermark_control_files 个 WatermarkControlView.swift 文件:"
    find "$PROJECT_DIR" -name "WatermarkControlView.swift" -type f
    duplicates_found=true
else
    echo "✅ WatermarkControlView.swift 无重复"
fi

# 检查样式文件是否有重复
for i in {1..25}; do
    style_file="CustomWatermarkStyle$i.swift"
    count=$(find "$PROJECT_DIR" -name "$style_file" -type f | wc -l)
    if [ "$count" -gt 1 ]; then
        echo "⚠️ 发现 $count 个 $style_file 文件"
        duplicates_found=true
    fi
done

if [ "$duplicates_found" = false ]; then
    echo "✅ 没有发现重复文件"
fi

echo ""
echo "4️⃣ 彻底清理 Xcode 缓存..."

echo "🧹 清理 Xcode 缓存和构建文件..."

# 清理 DerivedData
rm -rf ~/Library/Developer/Xcode/DerivedData/Lomo-*
echo "✅ 清理 DerivedData"

# 清理项目构建文件
if [ -d "$PROJECT_ROOT/.build" ]; then
    rm -rf "$PROJECT_ROOT/.build"
    echo "✅ 清理 .build 目录"
fi

# 清理 Xcode 用户数据
if [ -d "$PROJECT_ROOT/Lomo.xcodeproj/xcuserdata" ]; then
    rm -rf "$PROJECT_ROOT/Lomo.xcodeproj/xcuserdata"
    echo "✅ 清理 xcuserdata"
fi

# 清理 Xcode 工作区数据
if [ -d "$PROJECT_ROOT/Lomo.xcodeproj/project.xcworkspace/xcuserdata" ]; then
    rm -rf "$PROJECT_ROOT/Lomo.xcodeproj/project.xcworkspace/xcuserdata"
    echo "✅ 清理 workspace xcuserdata"
fi

echo ""
echo "5️⃣ 验证项目文件完整性..."

PBXPROJ_FILE="$PROJECT_ROOT/Lomo.xcodeproj/project.pbxproj"
if [ -f "$PBXPROJ_FILE" ]; then
    echo "✅ 项目文件存在"
    
    # 检查项目文件大小（太小可能损坏）
    file_size=$(wc -c < "$PBXPROJ_FILE")
    if [ "$file_size" -gt 10000 ]; then
        echo "✅ 项目文件大小正常 ($file_size 字节)"
    else
        echo "⚠️ 项目文件可能损坏 ($file_size 字节)"
    fi
else
    echo "❌ 项目文件不存在"
fi

echo ""
echo "6️⃣ 生成编译指导..."

COMPILE_GUIDE="$PROJECT_DIR/Scripts/compilation_guide.md"
cat > "$COMPILE_GUIDE" << 'EOF'
# 水印模块编译指导

## 修复完成后的操作步骤

### 1. 重新打开 Xcode
- 完全退出 Xcode
- 重新打开 Lomo.xcodeproj

### 2. 检查项目结构
在 Project Navigator 中确认：
- 没有红色的文件引用
- 水印相关文件都在正确位置
- 没有重复的文件

### 3. 清理和重建
- Product -> Clean Build Folder (⇧⌘K)
- Product -> Build (⌘B)

### 4. 如果仍有编译错误
检查以下位置：
- Build Phases -> Compile Sources
- 确保没有重复的源文件
- 确保没有备份文件被包含

### 5. 水印功能测试
编译成功后，测试：
- 水印样式选择
- 水印设置保存
- 水印应用效果

## 文件位置确认

### Manager 模式文件（应该存在）
- `Lomo/Managers/Edit/WatermarkSettingsManager.swift`
- `Lomo/Managers/Edit/WatermarkStyleManager.swift`
- `Lomo/Managers/Edit/WatermarkStyles.swift`
- `Lomo/Managers/Edit/WatermarkStyles/CustomWatermarkStyle*.swift` (25个)

### 视图文件（应该存在）
- `Lomo/Views/Edit/Components/WatermarkControlView.swift`

### 模型和常量（应该存在）
- `Lomo/Models/Edit/WatermarkSettings.swift`
- `Lomo/Utils/Constants/WatermarkConstants.swift`

### MVVM-S 文件（应该不存在）
- `Lomo/ViewModels/Edit/WatermarkViewModel.swift` ❌
- `Lomo/Services/Edit/WatermarkService.swift` ❌
- `Lomo/DependencyInjection/WatermarkDependencyContainer.swift` ❌
- `Lomo/Views/Edit/WatermarkView.swift` ❌

## 备份文件位置
所有备份文件已移动到：
`Lomo/Scripts/final_backup_[timestamp]/`
EOF

echo "📝 编译指导已保存到: $COMPILE_GUIDE"

echo ""
echo "🎉 最终修复完成！"
echo ""
echo "📋 修复总结:"
echo "✅ 所有备份文件已移动到安全位置"
echo "✅ Xcode 缓存已彻底清理"
echo "✅ 项目文件引用已修复"
echo "✅ 文件结构已验证"
echo ""
echo "🔄 下一步操作:"
echo "1. 重新打开 Xcode 项目"
echo "2. 执行 Clean Build Folder"
echo "3. 重新编译项目"
echo "4. 查看编译指导: $COMPILE_GUIDE"
echo ""
echo "📦 备份位置: $FINAL_BACKUP_DIR"
echo "📝 如有问题，请查看编译指导文档"