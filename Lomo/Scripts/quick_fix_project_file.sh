#!/bin/bash
# Copyright (c) 2025 LoniceraLab. All rights reserved.

# 快速修复 Xcode 项目文件中的备份文件引用

PROJECT_ROOT="/Users/<USER>/Lomo"
PBXPROJ_FILE="$PROJECT_ROOT/Lomo.xcodeproj/project.pbxproj"

if [ ! -f "$PBXPROJ_FILE" ]; then
    echo "❌ 无法找到项目文件"
    exit 1
fi

echo "🔧 快速修复项目文件..."

# 备份项目文件
cp "$PBXPROJ_FILE" "$PBXPROJ_FILE.backup_$(date +%Y%m%d_%H%M%S)"
echo "📦 已备份项目文件"

# 移除备份文件引用
sed -i '' '/\.backup/d' "$PBXPROJ_FILE"
sed -i '' '/\.duplicate_backup/d' "$PBXPROJ_FILE"
sed -i '' '/\.textinput_backup/d' "$PBXPROJ_FILE"

echo "✅ 已移除备份文件引用"
echo "🔄 请重新打开 Xcode 项目"
