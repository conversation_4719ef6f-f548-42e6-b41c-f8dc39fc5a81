#!/bin/bash
# Copyright (c) 2025 LoniceraLab. All rights reserved.

# 水印模块回退到 Lomo(76) 版本脚本

echo "🔄 开始将水印模块回退到 Lomo(76) 版本..."
echo "📍 源目录: /Users/<USER>/Lomo/Lomo(76)"
echo "📍 目标目录: /Users/<USER>/Lomo/Lomo"
echo ""

# 设置路径变量
SOURCE_DIR="/Users/<USER>/Lomo/Lomo(76)"
TARGET_DIR="/Users/<USER>/Lomo/Lomo"

# 检查源目录是否存在
if [ ! -d "$SOURCE_DIR" ]; then
    echo "❌ 错误: 源目录 $SOURCE_DIR 不存在"
    exit 1
fi

# 创建备份目录
BACKUP_DIR="$TARGET_DIR/Scripts/watermark_current_backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

echo "📦 创建当前水印模块备份到: $BACKUP_DIR"

# 备份当前的水印相关文件
echo "1️⃣ 备份当前水印文件..."

# 备份 ViewModels
if [ -f "$TARGET_DIR/ViewModels/Edit/WatermarkViewModel.swift" ]; then
    cp "$TARGET_DIR/ViewModels/Edit/WatermarkViewModel.swift" "$BACKUP_DIR/"
    echo "✅ 备份 WatermarkViewModel.swift"
fi

# 备份 Services
if [ -f "$TARGET_DIR/Services/Edit/WatermarkService.swift" ]; then
    cp "$TARGET_DIR/Services/Edit/WatermarkService.swift" "$BACKUP_DIR/"
    echo "✅ 备份 WatermarkService.swift"
fi

# 备份 DependencyInjection
if [ -f "$TARGET_DIR/DependencyInjection/WatermarkDependencyContainer.swift" ]; then
    cp "$TARGET_DIR/DependencyInjection/WatermarkDependencyContainer.swift" "$BACKUP_DIR/"
    echo "✅ 备份 WatermarkDependencyContainer.swift"
fi

# 备份 Views
if [ -f "$TARGET_DIR/Views/Edit/WatermarkView.swift" ]; then
    cp "$TARGET_DIR/Views/Edit/WatermarkView.swift" "$BACKUP_DIR/"
    echo "✅ 备份 WatermarkView.swift"
fi

# 备份 Components
if [ -d "$TARGET_DIR/Views/Edit/Components" ]; then
    find "$TARGET_DIR/Views/Edit/Components" -name "*Watermark*" -type f -exec cp {} "$BACKUP_DIR/" \;
    echo "✅ 备份水印组件文件"
fi

echo ""
echo "2️⃣ 开始从 Lomo(76) 恢复水印文件..."

# 恢复 Managers (Lomo(76) 使用的是 Manager 模式)
echo "📁 恢复 Managers..."
if [ -d "$SOURCE_DIR/Lomo/Managers/Edit" ]; then
    # 确保目标目录存在
    mkdir -p "$TARGET_DIR/Managers/Edit"
    
    # 复制水印相关的 Manager 文件
    find "$SOURCE_DIR/Lomo/Managers/Edit" -name "*Watermark*" -type f -exec cp {} "$TARGET_DIR/Managers/Edit/" \;
    echo "✅ 恢复水印 Manager 文件"
    
    # 复制水印样式目录
    if [ -d "$SOURCE_DIR/Lomo/Managers/Edit/WatermarkStyles" ]; then
        cp -r "$SOURCE_DIR/Lomo/Managers/Edit/WatermarkStyles" "$TARGET_DIR/Managers/Edit/"
        echo "✅ 恢复水印样式文件"
    fi
fi

# 恢复 Models
echo "📁 恢复 Models..."
if [ -f "$SOURCE_DIR/Lomo/Models/Edit/WatermarkSettings.swift" ]; then
    mkdir -p "$TARGET_DIR/Models/Edit"
    cp "$SOURCE_DIR/Lomo/Models/Edit/WatermarkSettings.swift" "$TARGET_DIR/Models/Edit/"
    echo "✅ 恢复 WatermarkSettings.swift"
fi

# 恢复 Utils/Constants
echo "📁 恢复 Constants..."
if [ -f "$SOURCE_DIR/Lomo/Utils/Constants/WatermarkConstants.swift" ]; then
    mkdir -p "$TARGET_DIR/Utils/Constants"
    cp "$SOURCE_DIR/Lomo/Utils/Constants/WatermarkConstants.swift" "$TARGET_DIR/Utils/Constants/"
    echo "✅ 恢复 WatermarkConstants.swift"
fi

# 恢复 Views/Edit/Components
echo "📁 恢复 View Components..."
if [ -f "$SOURCE_DIR/Lomo/Views/Edit/Components/WatermarkControlView.swift" ]; then
    mkdir -p "$TARGET_DIR/Views/Edit/Components"
    cp "$SOURCE_DIR/Lomo/Views/Edit/Components/WatermarkControlView.swift" "$TARGET_DIR/Views/Edit/Components/"
    echo "✅ 恢复 WatermarkControlView.swift"
fi

# 恢复备份文件（如果存在）
if [ -f "$SOURCE_DIR/Lomo/Views/Edit/Components/WatermarkControlView.swift.bak" ]; then
    cp "$SOURCE_DIR/Lomo/Views/Edit/Components/WatermarkControlView.swift.bak" "$TARGET_DIR/Views/Edit/Components/"
    echo "✅ 恢复 WatermarkControlView.swift.bak"
fi

echo ""
echo "3️⃣ 清理当前的 MVVM-S 架构文件..."

# 删除当前的 MVVM-S 架构文件（因为 Lomo(76) 使用的是 Manager 模式）
if [ -f "$TARGET_DIR/ViewModels/Edit/WatermarkViewModel.swift" ]; then
    rm "$TARGET_DIR/ViewModels/Edit/WatermarkViewModel.swift"
    echo "🗑️ 删除 WatermarkViewModel.swift (MVVM-S)"
fi

if [ -f "$TARGET_DIR/Services/Edit/WatermarkService.swift" ]; then
    rm "$TARGET_DIR/Services/Edit/WatermarkService.swift"
    echo "🗑️ 删除 WatermarkService.swift (MVVM-S)"
fi

if [ -f "$TARGET_DIR/DependencyInjection/WatermarkDependencyContainer.swift" ]; then
    rm "$TARGET_DIR/DependencyInjection/WatermarkDependencyContainer.swift"
    echo "🗑️ 删除 WatermarkDependencyContainer.swift (MVVM-S)"
fi

if [ -f "$TARGET_DIR/Views/Edit/WatermarkView.swift" ]; then
    rm "$TARGET_DIR/Views/Edit/WatermarkView.swift"
    echo "🗑️ 删除 WatermarkView.swift (MVVM-S)"
fi

# 删除其他 MVVM-S 相关的水印组件
find "$TARGET_DIR/Views/Edit/Components" -name "Watermark*View.swift" -not -name "WatermarkControlView.swift" -delete 2>/dev/null
echo "🗑️ 删除其他 MVVM-S 水印组件"

echo ""
echo "4️⃣ 验证恢复结果..."

# 检查关键文件是否存在
echo "📋 检查恢复的文件:"
echo "Manager 模式文件:"
[ -f "$TARGET_DIR/Managers/Edit/WatermarkSettingsManager.swift" ] && echo "✅ WatermarkSettingsManager.swift" || echo "❌ WatermarkSettingsManager.swift"
[ -f "$TARGET_DIR/Managers/Edit/WatermarkStyleManager.swift" ] && echo "✅ WatermarkStyleManager.swift" || echo "❌ WatermarkStyleManager.swift"
[ -f "$TARGET_DIR/Managers/Edit/WatermarkStyles.swift" ] && echo "✅ WatermarkStyles.swift" || echo "❌ WatermarkStyles.swift"
[ -d "$TARGET_DIR/Managers/Edit/WatermarkStyles" ] && echo "✅ WatermarkStyles 目录" || echo "❌ WatermarkStyles 目录"

echo ""
echo "模型和常量文件:"
[ -f "$TARGET_DIR/Models/Edit/WatermarkSettings.swift" ] && echo "✅ WatermarkSettings.swift" || echo "❌ WatermarkSettings.swift"
[ -f "$TARGET_DIR/Utils/Constants/WatermarkConstants.swift" ] && echo "✅ WatermarkConstants.swift" || echo "❌ WatermarkConstants.swift"

echo ""
echo "视图文件:"
[ -f "$TARGET_DIR/Views/Edit/Components/WatermarkControlView.swift" ] && echo "✅ WatermarkControlView.swift" || echo "❌ WatermarkControlView.swift"

echo ""
echo "MVVM-S 文件清理检查:"
[ ! -f "$TARGET_DIR/ViewModels/Edit/WatermarkViewModel.swift" ] && echo "✅ WatermarkViewModel.swift 已删除" || echo "❌ WatermarkViewModel.swift 仍存在"
[ ! -f "$TARGET_DIR/Services/Edit/WatermarkService.swift" ] && echo "✅ WatermarkService.swift 已删除" || echo "❌ WatermarkService.swift 仍存在"
[ ! -f "$TARGET_DIR/DependencyInjection/WatermarkDependencyContainer.swift" ] && echo "✅ WatermarkDependencyContainer.swift 已删除" || echo "❌ WatermarkDependencyContainer.swift 仍存在"

echo ""
echo "🎉 水印模块回退完成！"
echo "📦 当前版本备份位置: $BACKUP_DIR"
echo "📋 现在使用的是 Lomo(76) 版本的 Manager 模式水印模块"
echo ""
echo "⚠️ 注意事项:"
echo "1. 请检查项目编译是否正常"
echo "2. 可能需要更新相关的引用和调用"
echo "3. 如需回滚，备份文件在: $BACKUP_DIR"