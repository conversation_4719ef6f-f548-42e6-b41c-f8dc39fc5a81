#!/bin/bash
# Copyright (c) 2025 LoniceraLab. All rights reserved.

# 验证水印模块回退到 Lomo(76) 版本的脚本

echo "🔍 验证水印模块回退状态..."
echo "📍 项目目录: /Users/<USER>/Lomo/Lomo"
echo ""

# 设置路径变量
PROJECT_DIR="/Users/<USER>/Lomo/Lomo"

# 检查 Manager 模式文件
echo "1️⃣ 检查 Manager 模式文件..."
echo "📁 Managers/Edit/ 目录:"

if [ -f "$PROJECT_DIR/Managers/Edit/WatermarkSettingsManager.swift" ]; then
    echo "✅ WatermarkSettingsManager.swift 存在"
    # 检查是否使用单例模式
    if grep -q "static let shared" "$PROJECT_DIR/Managers/Edit/WatermarkSettingsManager.swift"; then
        echo "   📋 使用单例模式 (Manager 架构特征)"
    fi
else
    echo "❌ WatermarkSettingsManager.swift 不存在"
fi

if [ -f "$PROJECT_DIR/Managers/Edit/WatermarkStyleManager.swift" ]; then
    echo "✅ WatermarkStyleManager.swift 存在"
else
    echo "❌ WatermarkStyleManager.swift 不存在"
fi

if [ -f "$PROJECT_DIR/Managers/Edit/WatermarkStyles.swift" ]; then
    echo "✅ WatermarkStyles.swift 存在"
else
    echo "❌ WatermarkStyles.swift 不存在"
fi

if [ -d "$PROJECT_DIR/Managers/Edit/WatermarkStyles" ]; then
    style_count=$(find "$PROJECT_DIR/Managers/Edit/WatermarkStyles" -name "CustomWatermarkStyle*.swift" | wc -l)
    echo "✅ WatermarkStyles 目录存在，包含 $style_count 个样式文件"
else
    echo "❌ WatermarkStyles 目录不存在"
fi

echo ""

# 检查模型和常量文件
echo "2️⃣ 检查模型和常量文件..."

if [ -f "$PROJECT_DIR/Models/Edit/WatermarkSettings.swift" ]; then
    echo "✅ WatermarkSettings.swift 存在"
else
    echo "❌ WatermarkSettings.swift 不存在"
fi

if [ -f "$PROJECT_DIR/Utils/Constants/WatermarkConstants.swift" ]; then
    echo "✅ WatermarkConstants.swift 存在"
else
    echo "❌ WatermarkConstants.swift 不存在"
fi

echo ""

# 检查视图文件
echo "3️⃣ 检查视图文件..."

if [ -f "$PROJECT_DIR/Views/Edit/Components/WatermarkControlView.swift" ]; then
    echo "✅ WatermarkControlView.swift 存在"
    # 检查是否包含 Manager 调用
    if grep -q "WatermarkSettingsManager.shared\|WatermarkStyleManager.shared" "$PROJECT_DIR/Views/Edit/Components/WatermarkControlView.swift"; then
        echo "   📋 包含 Manager 调用 (Manager 架构特征)"
    fi
else
    echo "❌ WatermarkControlView.swift 不存在"
fi

echo ""

# 检查 MVVM-S 文件是否已清理
echo "4️⃣ 检查 MVVM-S 文件清理状态..."

if [ ! -f "$PROJECT_DIR/ViewModels/Edit/WatermarkViewModel.swift" ]; then
    echo "✅ WatermarkViewModel.swift 已删除"
else
    echo "❌ WatermarkViewModel.swift 仍存在"
fi

if [ ! -f "$PROJECT_DIR/Services/Edit/WatermarkService.swift" ]; then
    echo "✅ WatermarkService.swift 已删除"
else
    echo "❌ WatermarkService.swift 仍存在"
fi

if [ ! -f "$PROJECT_DIR/DependencyInjection/WatermarkDependencyContainer.swift" ]; then
    echo "✅ WatermarkDependencyContainer.swift 已删除"
else
    echo "❌ WatermarkDependencyContainer.swift 仍存在"
fi

if [ ! -f "$PROJECT_DIR/Views/Edit/WatermarkView.swift" ]; then
    echo "✅ WatermarkView.swift 已删除"
else
    echo "❌ WatermarkView.swift 仍存在"
fi

echo ""

# 检查其他可能的 MVVM-S 水印组件
echo "5️⃣ 检查其他 MVVM-S 组件清理状态..."
mvvm_components=$(find "$PROJECT_DIR/Views/Edit/Components" -name "Watermark*View.swift" -not -name "WatermarkControlView.swift" 2>/dev/null | wc -l)
if [ "$mvvm_components" -eq 0 ]; then
    echo "✅ 其他 MVVM-S 水印组件已清理"
else
    echo "⚠️ 发现 $mvvm_components 个其他 MVVM-S 水印组件:"
    find "$PROJECT_DIR/Views/Edit/Components" -name "Watermark*View.swift" -not -name "WatermarkControlView.swift" 2>/dev/null
fi

echo ""

# 统计文件数量
echo "6️⃣ 文件统计..."
manager_files=$(find "$PROJECT_DIR/Managers/Edit" -name "*Watermark*" -type f 2>/dev/null | wc -l)
style_files=$(find "$PROJECT_DIR/Managers/Edit/WatermarkStyles" -name "*.swift" -type f 2>/dev/null | wc -l)
total_watermark_files=$(find "$PROJECT_DIR" -name "*Watermark*" -type f 2>/dev/null | wc -l)

echo "📊 Manager 文件: $manager_files 个"
echo "📊 样式文件: $style_files 个"
echo "📊 总水印文件: $total_watermark_files 个"

echo ""

# 检查备份
echo "7️⃣ 检查备份状态..."
backup_dirs=$(find "$PROJECT_DIR/Scripts" -name "watermark_current_backup_*" -type d 2>/dev/null | wc -l)
if [ "$backup_dirs" -gt 0 ]; then
    echo "✅ 发现 $backup_dirs 个备份目录:"
    find "$PROJECT_DIR/Scripts" -name "watermark_current_backup_*" -type d 2>/dev/null | tail -3
else
    echo "⚠️ 未发现备份目录"
fi

echo ""

# 编译检查建议
echo "8️⃣ 编译检查建议..."
echo "📋 建议执行以下检查:"
echo "1. 在 Xcode 中编译项目，检查是否有编译错误"
echo "2. 检查水印功能是否正常工作"
echo "3. 确认所有水印样式都能正确显示"
echo "4. 验证水印设置的保存和加载功能"

echo ""

# 总结
echo "🎯 回退状态总结:"
if [ -f "$PROJECT_DIR/Managers/Edit/WatermarkSettingsManager.swift" ] && \
   [ -f "$PROJECT_DIR/Managers/Edit/WatermarkStyleManager.swift" ] && \
   [ ! -f "$PROJECT_DIR/ViewModels/Edit/WatermarkViewModel.swift" ] && \
   [ ! -f "$PROJECT_DIR/Services/Edit/WatermarkService.swift" ]; then
    echo "✅ 水印模块已成功回退到 Lomo(76) 的 Manager 模式"
    echo "📋 架构模式: Manager 模式 (使用单例)"
    echo "📋 文件结构: 符合 Lomo(76) 版本"
else
    echo "⚠️ 回退可能不完整，请检查上述文件状态"
fi

echo ""
echo "🔗 相关文档:"
echo "- 水印8更新说明.md"
echo "- 项目根目录的相关说明文档"